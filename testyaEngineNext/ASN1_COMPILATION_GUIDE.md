# ASN.1协议解析器编译配置指南

## 概述

本项目现在支持可选的ASN.1协议解析器编译。默认情况下，ASN.1协议解析器**不会被编译**，以减少编译时间和依赖要求。

## 快速开始

### 方法1: 使用配置脚本 (推荐)

```bash
# 查看当前ASN.1编译状态
./configure_asn1.sh status

# 启用ASN.1协议解析器编译
./configure_asn1.sh enable

# 禁用ASN.1协议解析器编译
./configure_asn1.sh disable

# 显示帮助信息
./configure_asn1.sh help
```

### 方法2: 手动CMake配置

```bash
# 启用ASN.1编译
rm -rf build
mkdir build && cd build
cmake -DENABLE_ASN1_PROTOCOLS=ON ..
make

# 禁用ASN.1编译 (默认)
rm -rf build
mkdir build && cd build
cmake -DENABLE_ASN1_PROTOCOLS=OFF ..
# 或者简单地
cmake ..
make
```

## ASN.1编译选项详情

### 编译选项

- **选项名称**: `ENABLE_ASN1_PROTOCOLS`
- **类型**: BOOL
- **默认值**: `OFF` (禁用)
- **描述**: 控制是否编译ASN.1协议解析器

### 启用ASN.1时包含的协议

当`ENABLE_ASN1_PROTOCOLS=ON`时，以下协议解析器会被编译：

- **SNMP** - 简单网络管理协议
- **H.225** - H.323呼叫信令协议  
- **H.245** - H.323媒体控制协议

### 禁用ASN.1时的影响

当`ENABLE_ASN1_PROTOCOLS=OFF`时：

- ASN.1相关的代码生成被跳过
- ASN.1协议插件不会被编译
- ASN.1相关的单元测试被排除
- 不需要安装`asn1c`工具
- 编译时间显著减少

## 依赖要求

### 启用ASN.1时的额外依赖

```bash
# Ubuntu/Debian
sudo apt-get install asn1c python3

# CentOS/RHEL
sudo yum install asn1c python3
```

### 基础依赖 (始终需要)

```bash
# Ubuntu/Debian
sudo apt-get install build-essential cmake libglib2.0-dev libpcap-dev

# CentOS/RHEL  
sudo yum install gcc-c++ cmake glib2-devel libpcap-devel
```

## 编译示例

### 示例1: 默认编译 (不包含ASN.1)

```bash
git clone <repository>
cd testyaEngineNext
mkdir build && cd build
cmake ..
make
```

### 示例2: 包含ASN.1的完整编译

```bash
git clone <repository>
cd testyaEngineNext

# 使用配置脚本
./configure_asn1.sh enable

# 或手动配置
# rm -rf build
# mkdir build && cd build  
# cmake -DENABLE_ASN1_PROTOCOLS=ON ..

cd build
make
```

### 示例3: 验证ASN.1功能

```bash
# 启用ASN.1后，可以运行ASN.1验证脚本
./configure_asn1.sh enable
cd build && make
cd ..
./build_asn1.sh
```

## 单元测试

### ASN.1相关测试

当ASN.1启用时，以下测试文件会被包含：
- `snmp_asn1_test.cpp`
- `h225ras_asn1_test.cpp`

当ASN.1禁用时，这些测试文件会被自动排除。

### 运行测试

```bash
# 编译单元测试
cd build
make

# 运行单元测试
make unit
```

## 故障排除

### 常见问题

1. **asn1c未找到**
   ```
   错误: 未找到asn1c工具
   解决: sudo apt-get install asn1c
   ```

2. **CMake缓存问题**
   ```bash
   # 清理CMake缓存
   rm -rf build
   ./configure_asn1.sh enable  # 或 disable
   ```

3. **检查当前配置**
   ```bash
   ./configure_asn1.sh status
   ```

### 调试信息

启用ASN.1时，CMake会输出以下信息：
```
-- ASN.1 protocol parsers compilation is ENABLED
-- Setting up ASN.1 protocols...
-- Setting up ASN.1 parser for protocol: snmp
-- Setting up ASN.1 parser for protocol: h245  
-- Setting up ASN.1 parser for protocol: h225
-- Configuring ASN.1 protocol plugins...
```

禁用ASN.1时，CMake会输出：
```
-- ASN.1 protocol parsers compilation is DISABLED
-- ASN.1 protocols are disabled, skipping ASN.1 setup
-- ASN.1 protocols are disabled, skipping ASN.1 plugin configuration
```

## 开发指南

### 添加新的ASN.1协议

1. 启用ASN.1编译
2. 在`yaLiteDpi/plugins/asn1/protocols.cmake`中添加协议名称
3. 创建协议目录和ASN.1文件
4. 重新编译

### 性能考虑

- **禁用ASN.1**: 编译时间减少约30-50%
- **启用ASN.1**: 完整功能，支持所有协议解析

## 总结

ASN.1编译开关提供了灵活性：
- **开发环境**: 可以启用完整功能进行开发和测试
- **生产环境**: 可以禁用不需要的协议以减少资源消耗
- **CI/CD**: 可以根据需要选择性编译

默认禁用ASN.1确保了项目的轻量级特性，同时保持了完整功能的可用性。
