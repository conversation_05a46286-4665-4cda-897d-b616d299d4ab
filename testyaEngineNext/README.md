## libyaEngineNext 测试程序(演示插件开发与基本用法)
演示了一个虚构的协议 story 的解析，验证 tcp 重组效果;

## 构建步骤

### 依赖
构建并安装 libyaEngineNext;

**ASN.1协议支持 (可选)**:
- 默认情况下，ASN.1协议解析器**不会被编译**
- 如需ASN.1支持，需要安装asn1c工具：
```shell
# Ubuntu/Debian
sudo apt-get install asn1c

# CentOS/RHEL
sudo yum install asn1c
```

### 快速构建 (默认，不包含ASN.1)
```shell
cmake3 -S . -B build && cmake3 --build build
```

### ASN.1编译配置

#### 使用配置脚本 (推荐)
```shell
# 查看当前ASN.1编译状态
./configure_asn1.sh status

# 启用ASN.1协议解析器编译
./configure_asn1.sh enable

# 禁用ASN.1协议解析器编译 (默认)
./configure_asn1.sh disable

# 显示帮助信息
./configure_asn1.sh help
```

#### 手动CMake配置
```shell
# 启用ASN.1编译
cmake3 -S . -B build -DENABLE_ASN1_PROTOCOLS=ON
cmake3 --build build

# 禁用ASN.1编译 (默认)
cmake3 -S . -B build -DENABLE_ASN1_PROTOCOLS=OFF
cmake3 --build build
```

### 测试
```shell
echo "/usr/local/lib64" >>/etc/ld.so.conf && ldconfig
cmake3 --build build -t test
```

### ASN.1功能验证 (仅在启用ASN.1时)
```shell
./configure_asn1.sh enable
cd build && make
cd ..
./build_asn1.sh
```

## 支持的协议

### 基础协议 (始终编译)
- UDP, TCP, DNS, RTP, SIP, SMTP
- VLAN, LLC, STP, ISIS, PPP, CDP
- ICMP, IGMP, RSVP, GRE, GTP-U, MPLS
- TPKT, Q.931

### ASN.1协议 (可选编译)
- **SNMP** - 简单网络管理协议
- **H.225** - H.323呼叫信令协议
- **H.245** - H.323媒体控制协议

## 文档
- [ASN.1编译配置详细指南](ASN1_COMPILATION_GUIDE.md)
- [ASN.1开发指南](yaLiteDpi/plugins/asn1/README.md)
