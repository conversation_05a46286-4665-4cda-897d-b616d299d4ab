#!/bin/bash

# ASN.1协议解析器编译配置脚本
# 用于启用或禁用ASN.1协议解析器的编译

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}ASN.1协议解析器编译配置脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  enable    启用ASN.1协议解析器编译"
    echo "  disable   禁用ASN.1协议解析器编译"
    echo "  status    显示当前ASN.1编译状态"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 enable     # 启用ASN.1编译"
    echo "  $0 disable    # 禁用ASN.1编译"
    echo "  $0 status     # 查看当前状态"
    echo ""
}

# 检查当前状态
check_status() {
    if [ -f "build/CMakeCache.txt" ]; then
        if grep -q "ENABLE_ASN1_PROTOCOLS:BOOL=ON" build/CMakeCache.txt; then
            echo -e "${GREEN}✓ ASN.1协议解析器编译已启用${NC}"
            return 0
        elif grep -q "ENABLE_ASN1_PROTOCOLS:BOOL=OFF" build/CMakeCache.txt; then
            echo -e "${YELLOW}✗ ASN.1协议解析器编译已禁用${NC}"
            return 1
        else
            echo -e "${YELLOW}? ASN.1编译状态未明确设置 (默认禁用)${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}? 项目尚未配置，ASN.1编译状态未知 (默认禁用)${NC}"
        return 1
    fi
}

# 启用ASN.1编译
enable_asn1() {
    echo -e "${BLUE}正在启用ASN.1协议解析器编译...${NC}"
    
    # 检查必要工具
    if ! command -v asn1c &> /dev/null; then
        echo -e "${RED}错误: 未找到asn1c工具${NC}"
        echo "请安装asn1c: sudo apt-get install asn1c"
        exit 1
    fi
    
    # 重新配置项目
    echo "重新配置CMake项目..."
    if [ -d "build" ]; then
        rm -rf build
    fi
    
    mkdir -p build
    cd build
    
    echo "运行CMake配置 (启用ASN.1)..."
    cmake -DENABLE_ASN1_PROTOCOLS=ON .. || {
        echo -e "${RED}CMake配置失败${NC}"
        exit 1
    }
    
    cd ..
    
    echo -e "${GREEN}✓ ASN.1协议解析器编译已启用${NC}"
    echo ""
    echo "现在可以运行以下命令进行编译:"
    echo "  cd build && make"
    echo ""
    echo "或者运行ASN.1验证脚本:"
    echo "  ./build_asn1.sh"
}

# 禁用ASN.1编译
disable_asn1() {
    echo -e "${BLUE}正在禁用ASN.1协议解析器编译...${NC}"
    
    # 重新配置项目
    echo "重新配置CMake项目..."
    if [ -d "build" ]; then
        rm -rf build
    fi
    
    mkdir -p build
    cd build
    
    echo "运行CMake配置 (禁用ASN.1)..."
    cmake -DENABLE_ASN1_PROTOCOLS=OFF .. || {
        echo -e "${RED}CMake配置失败${NC}"
        exit 1
    }
    
    cd ..
    
    echo -e "${GREEN}✓ ASN.1协议解析器编译已禁用${NC}"
    echo ""
    echo "现在可以运行以下命令进行编译:"
    echo "  cd build && make"
    echo ""
    echo "注意: ASN.1相关的协议解析器将不会被编译"
}

# 主逻辑
case "${1:-help}" in
    "enable")
        enable_asn1
        ;;
    "disable")
        disable_asn1
        ;;
    "status")
        check_status
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$1'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
