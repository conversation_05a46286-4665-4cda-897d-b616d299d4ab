#include <exception>
#include <iostream>
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_ringbuf.h>
#include <yaEngineNext/nxt_util.h>
#include <yaEngineNext/nxt_isession_proxy.h>
#include <yaProtoRecord/precord.h>

#include <vector>
#include <string>

#include <gmock/gmock.h>

using ::testing::_;

// EQ_OP 取值有:
// EQ, NE, LE, LT, GE, GT
// STREQ, STRNE, STRCASEEQ, STRCASENE
// FLOAT_EQ, DOUBLE_EQ
#define EXPECT_PRECORD_MATCH(EQ_OP, precord, field_name, TYPE, expect_value) \
    do {                                                                     \
    pfield_t *field      = precord_field_get_by_name(precord, field_name);   \
    ASSERT_TRUE(field   != NULL);                                            \
    ya_fvalue_t *fvalue  = precord_field_get_fvalue(field);                  \
    ASSERT_TRUE(fvalue  != NULL);                                            \
    auto actual_value    = ya_fvalue_get_##TYPE(fvalue);                     \
    EXPECT_##EQ_OP(actual_value, expect_value);                              \
    } while (0)

class MockSessionProxy : public nxt_ISessionProxy
{
public:
    MOCK_METHOD(int, readToRingbuf, (nxt_direction_enum direction, nxt_ringbuf_t *rbuf,
                                     uint32_t read_len, nxt_stream_read_res_t *read_status), (override));

    MOCK_METHOD(int, onEvent, (nxt_event event, nxt_mbuf_t *mbuf, precord_t *precord), (override));
};

class test_dissector : public testing::Test
{
public:
    static const bool kShowMsg    = true;
    static const bool kNotShowMsg = false;

public:
    void SetUp() override
    {
        engine_ = nxt_engine_create(NULL);
        session_ = nxt_session_create_psudo_session(engine_, &proxy_);

        // 模拟 session 被识别为 http, 让 session userdata 被创建;
        auto testDissector = nxt_dissector_get_by_name("smtp");
        nxt_session_on_proto_recognized(engine_, session_, testDissector);
    }

    void TearDown() override
    {
        clearMsgs();

        nxt_session_destory_psudo_session(engine_, session_);
        nxt_engine_destroy(engine_);
    }

    void clearMsgs()
    {
        for (auto& precord : msgArray_)
        {
            precord_destroy(precord);
        }

        msgArray_.clear();
    }

    void testDissect(const std::vector<std::string> &stream, nxt_direction_enum dir = NXT_DIR_C2S, bool showMsg = kNotShowMsg)
    {
        // 处理的消息为 C2S 方向;
        nxt_engine_regzone_put_direction(engine_, dir);

        // mock session->readToRingbuf 方法，将 http_stream 中的数据
        // 喂给 ringbuf, 让 dissector 流程顺畅;
        auto iter = stream.begin();
        EXPECT_CALL(proxy_, readToRingbuf(_, _, _, _))
            .WillRepeatedly([&](nxt_direction_enum direction _U_, nxt_ringbuf_t *rbuf,
                                uint32_t readLen _U_, nxt_stream_read_res_t *readStatus _U_)
            {
                try {
                    if (iter == stream.end())
                    {
                        return 0;
                    }

                    auto segment = *iter++;
                    nxt_ringbuf_push_back(rbuf, (const uint8_t*)segment.data(), segment.length());
                    return (int)segment.length();
                } catch (const std::exception& e) {
                    std::cerr << "Exception caught: " << e.what() << std::endl;
                    return -1;
                }

            });


        // mock session->onEvent 方法，将解析过程中产生的 precord 保存到 outputPrecords 中;
        EXPECT_CALL(proxy_, onEvent(_, _, _))
            .WillRepeatedly([&](nxt_event event, nxt_mbuf_t *mbuf _U_, precord_t *precord)
            {
                if (NXT_EVENT_SESSION_MESSAGE != event)
                {
                    return 0;
                }

                auto precordCopy = precord_clone(precord);
                msgArray_.push_back(precordCopy);
                return 0;
            });

        // 清空消息，防止上次解析结果残留;
        clearMsgs();

        // 执行被测试的函数: http_dissect
        nxt_dissector_t *testDissector = nxt_dissector_get_by_name("smtp");
        nxt_dissector_do_dissect(engine_, testDissector, session_, NULL);

        // 打印结果
        if (showMsg)
        {
            for (auto &precord : msgArray_)
            {
                nxt_util_show_precord(precord);
            }
        }
    }

protected:
    nxt_engine_t            *engine_  = NULL;
    nxt_session_t           *session_ = NULL;
    MockSessionProxy         proxy_;
    std::vector<precord_t *> msgArray_;
};

TEST_F(test_dissector, smtp_basic)
{
    // 准备测试数据
    std::vector<std::string> stream = {
        {"220 WIN-9ETNELBLVG7 ESMTP\r\n"},
        {"EHLO WINDOWS7-10\r\n"},
        {"250-WIN-9ETNELBLVG7\r\n"},
        {"250-SIZE 102400000\r\n"},
        {"250-AUTH LOGIN\r\n"},
        {"250 HELP\r\n"},
        {"AUTH LOGIN\r\n"},
        {"334 VXNlcm5hbWU6\r\n"},
        {"cmVjdjJAdGVzdC5vcmc=\r\n"},
        {"334 UGFzc3dvcmQ6\r\n"},
        {"cmVjdjI=\r\n"},
        {"235 authenticated.\r\n"},
        {"MAIL FROM: <<EMAIL>> SIZE=1524572\r\n"},
        {"250 OK\r\n"},
        {"RCPT TO: <<EMAIL>>\r\n"},
        {"250 OK\r\n"},
        {"DATA\r\n"},
        {"354 OK, send.\r\n"},
        {
         "Date: Wed, 7 Dec 2022 16:59:10 +0800\r\n"
         "From: \"<EMAIL>\" <<EMAIL>>\r\n"
         "To: admin <<EMAIL>>\r\n"
         "Subject: EULA1\r\n"
         "X-Priority: 3\r\n"
         "X-GUID: B6CC77AE-D593-4C20-8043-6977586229A9\r\n"
         "X-Has-Attach: yes\r\n"
         "X-Mailer: Foxmail 7.2.18.106[cn]\r\n"
         "Mime-Version: 1.0\r\n"
         "\r\n"
         "this is body\r\n.\r\n"
        },

    };
    // 执行测试
    this->testDissect(stream);
    // this->testDissect(stream1, NXT_DIR_S2C, kShowMsg);

    ASSERT_EQ(msgArray_.size(), 1);
    nxt_util_show_precord(msgArray_[0]);

    // 校验解析出来的 msg
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "date",       string, "Wed, 7 Dec 2022 16:59:10 +0800");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "mail_from",  string, "<<EMAIL>> SIZE=1524572");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "from",       string, "\"<EMAIL>\" <<EMAIL>>");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "URI",        string, "/seupdater.gif?res=0");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Version",    string, "HTTP/1.1");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Host",       string, "p3p.sogou.com");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "User-Agent", string, "MicroMessenger Client");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Cookie",     string, "IMEVER=9.8.0.3746");
}


TEST_F(test_dissector, smtp_multi)
{
    // 准备测试数据
    std::vector<std::string> stream = {
        {"220 WIN-9ETNELBLVG7 ESMTP\r\n"},
        {"EHLO WINDOWS7-10\r\n"},
        {"250-WIN-9ETNELBLVG7\r\n"},
        {"250-SIZE 102400000\r\n"},
        {"250-AUTH LOGIN\r\n"},
        {"250 HELP\r\n"},
        {"AUTH LOGIN\r\n"},
        {"334 VXNlcm5hbWU6\r\n"},
        {"cmVjdjJAdGVzdC5vcmc=\r\n"},
        {"334 UGFzc3dvcmQ6\r\n"},
        {"cmVjdjI=\r\n"},
        {"235 authenticated.\r\n"},
        {"MAIL FROM: <<EMAIL>> SIZE=1524572\r\n"},
        {"250 OK\r\n"},
        {"RCPT TO: <<EMAIL>>\r\n"},
        {"250 OK\r\n"},
        {"DATA\r\n"},
        {"354 OK, send.\r\n"},
        {
         "Date: Wed, 7 Dec 2022 16:59:10 +0800\r\n"
         "From: \"<EMAIL>\" <<EMAIL>>\r\n"
         "To: admin <<EMAIL>>\r\n"
         "Subject: EULA1\r\n"
         "X-Priority: 3\r\n"
         "X-GUID: B6CC77AE-D593-4C20-8043-6977586229A9\r\n"
         "X-Has-Attach: yes\r\n"
         "X-Mailer: Foxmail 7.2.18.106[cn]\r\n"
         "Mime-Version: 1.0\r\n"
         "\r\n"
         "this is body\r\n.\r\n"
        },
        {"MAIL FROM: <<EMAIL>> SIZE=1524572\r\n"},
        {"250 OK\r\n"},
        {"RCPT TO: <<EMAIL>>\r\n"},
        {"250 OK\r\n"},
        {"DATA\r\n"},
        {"354 OK, send.\r\n"},
        {
         "Date: Wed, 7 Dec 2022 16:59:10 +0800\r\n"
         "From: \"<EMAIL>\" <<EMAIL>>\r\n"
         "To: admin <<EMAIL>>\r\n"
         "Subject: EULA1\r\n"
         "X-Priority: 3\r\n"
         "X-GUID: B6CC77AE-D593-4C20-8043-6977586229A9\r\n"
         "X-Has-Attach: yes\r\n"
         "X-Mailer: Foxmail 7.2.18.106[cn]\r\n"
         "Mime-Version: 1.0\r\n"
         "\r\n"
         "this is body\r\n.\r\n"
        },

    };
    // 执行测试
    this->testDissect(stream);
    // this->testDissect(stream1, NXT_DIR_S2C, kShowMsg);

    ASSERT_EQ(msgArray_.size(), 2);
    nxt_util_show_precord(msgArray_[0]);
    nxt_util_show_precord(msgArray_[1]);

    // 校验解析出来的 msg
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "date",       string, "Wed, 7 Dec 2022 16:59:10 +0800");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "mail_from",  string, "<<EMAIL>> SIZE=1524572");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Version",    string, "HTTP/1.1");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Host",       string, "p3p.sogou.com");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "User-Agent", string, "MicroMessenger Client");
    // EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Cookie",     string, "IMEVER=9.8.0.3746");
}

