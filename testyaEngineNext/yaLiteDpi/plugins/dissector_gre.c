#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "gre"

static
int gre_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{

  // Check minimum GRE header length (4 bytes)
  if (nxt_mbuf_get_length(mbuf) < 4) {
    printf("GRE: insufficient data length (%d bytes, need at least 4)\n",
      nxt_mbuf_get_length(mbuf));
      return -1;
    }

    int packet_length = nxt_mbuf_get_length(mbuf);
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Parse GRE header
    uint16_t flags_version = nxt_mbuf_get_uint16_ntoh(mbuf, 0);
    uint16_t protocol_type = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    uint8_t version = flags_version & 0x07;
    bool checksum_present = (flags_version & 0x8000) != 0;
    bool key_present = (flags_version & 0x2000) != 0;
    bool sequence_present = (flags_version & 0x1000) != 0;
    bool ack_present = (flags_version & 0x0080) != 0; // Enhanced GRE (RFC 2637)

    // Record GRE basic fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "checksum_present", uinteger, checksum_present ? 1 : 0);
    precord_put(precord, "key_present", uinteger, key_present ? 1 : 0);
    precord_put(precord, "sequence_present", uinteger, sequence_present ? 1 : 0);
    precord_put(precord, "protocol_type", uinteger, protocol_type);

    // Add protType field (alias for protocol_type as required by 开发需求.md)
    precord_put(precord, "protType", uinteger, protocol_type);

    printf("GRE: Version=%d, Protocol=0x%04x, C=%d, K=%d, S=%d, A=%d\n",
           version, protocol_type, checksum_present, key_present, sequence_present, ack_present);

    int header_len = 4;
    int offset = 4;

    // Handle optional fields in order
    if (checksum_present && offset + 4 <= packet_length) {
        uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
        uint16_t reserved = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 2);
        printf("GRE: Checksum=0x%04x, Reserved=0x%04x\n", checksum, reserved);
        offset += 4;
        header_len += 4;
    }

    if (key_present && offset + 4 <= packet_length) {
        uint32_t key = nxt_mbuf_get_uint32_ntoh(mbuf, offset);

        // For PPTP (Enhanced GRE), the key field contains Call ID and Session ID
        if (protocol_type == 0x880B) { // PPTP protocol type
            uint16_t call_id = (key >> 16) & 0xFFFF;
            uint16_t session_id = key & 0xFFFF;
            precord_put(precord, "callID", uinteger, call_id);
            printf("GRE: PPTP Call ID=%d, Session ID=%d\n", call_id, session_id);
        } else {
            precord_put(precord, "callID", uinteger, key);
        }

        printf("GRE: Key=0x%08x\n", key);
        offset += 4;
        header_len += 4;
    }

    if (sequence_present && offset + 4 <= packet_length) {
        uint32_t sequence = nxt_mbuf_get_uint32_ntoh(mbuf, offset);
        precord_put(precord, "seqNum", uinteger, sequence);
        printf("GRE: Sequence Number=%u\n", sequence);
        offset += 4;
        header_len += 4;
    }

    if (ack_present && offset + 4 <= packet_length) {
        uint32_t ack_number = nxt_mbuf_get_uint32_ntoh(mbuf, offset);
        precord_put(precord, "ackNum", uinteger, ack_number);
        printf("GRE: Acknowledgment Number=%u\n", ack_number);
        offset += 4;
        header_len += 4;
    }

    // Extract source and destination IP from engine context
    // Use engine's packet zone to get IP addresses
    nxt_tuple_5_ipv4_t t5;
    if (nxt_engine_pktzone_get_t5_ipv4(engine, &t5) == 0) {
        uint32_t sip = t5.srcAddr;
        uint32_t dip = t5.dstAddr;

        precord_put(precord, "sip", uinteger, sip);
        precord_put(precord, "dip", uinteger, dip);

        // For demonstration, set some example values for sipCnt, sipAsn, sipCou
        // These would normally come from GeoIP or ASN databases
        precord_put(precord, "sipCnt", uinteger, 0); // Country code placeholder
        precord_put(precord, "sipAsn", uinteger, 0); // ASN placeholder
        precord_put(precord, "sipCou", uinteger, 0); // Country placeholder

        precord_put(precord, "dipCnt", uinteger, 0); // Country code placeholder
        precord_put(precord, "dipAsn", uinteger, 0); // ASN placeholder
        precord_put(precord, "dipCou", uinteger, 0); // Country placeholder

        printf("GRE: Source IP=%d.%d.%d.%d, Destination IP=%d.%d.%d.%d\n",
               (sip >> 24) & 0xFF, (sip >> 16) & 0xFF,
               (sip >> 8) & 0xFF, sip & 0xFF,
               (dip >> 24) & 0xFF, (dip >> 16) & 0xFF,
               (dip >> 8) & 0xFF, dip & 0xFF);
    }

    // Set handoff for encapsulated protocol
    nxt_handoff_set_key_of_number(engine, protocol_type);
    // nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);

    return header_len;
}

static
int gre_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "generic routing encapsulation");

    // Basic GRE header fields
    pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
    pschema_register_field(pschema, "checksum_present", YA_FT_UINT8, "checksum present flag");
    pschema_register_field(pschema, "key_present", YA_FT_UINT8, "key present flag");
    pschema_register_field(pschema, "sequence_present", YA_FT_UINT8, "sequence present flag");
    pschema_register_field_ex(pschema, "protocol_type", YA_FT_UINT16, "protocol type", YA_DISPLAY_BASE_HEX);

    // Required fields from 开发需求.md
    pschema_register_field_ex(pschema, "protType", YA_FT_UINT16, "protocol type (alias)", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "callID", YA_FT_UINT32, "call identifier");
    pschema_register_field(pschema, "seqNum", YA_FT_UINT32, "sequence number");
    pschema_register_field(pschema, "ackNum", YA_FT_UINT32, "acknowledgment number");

    // IP address fields
    pschema_register_field_ex(pschema, "sip", YA_FT_UINT32, "source IP address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "sipCnt", YA_FT_UINT32, "source IP country code");
    pschema_register_field(pschema, "sipAsn", YA_FT_UINT32, "source IP ASN");
    pschema_register_field(pschema, "sipCou", YA_FT_UINT32, "source IP country");

    pschema_register_field_ex(pschema, "dip", YA_FT_UINT32, "destination IP address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "dipCnt", YA_FT_UINT32, "destination IP country code");
    pschema_register_field(pschema, "dipAsn", YA_FT_UINT32, "destination IP ASN");
    pschema_register_field(pschema, "dipCou", YA_FT_UINT32, "destination IP country");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "gre",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = gre_schema_reg,
    .dissectFun   = gre_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(gre)
{
    nxt_dissector_register(&gDissectorDef);
    
    // Register handoff rules for common encapsulated protocols
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x0800, "ipv4");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x86DD, "ipv6");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x0806, "arp");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ipv4", 0x2f, "gre");

    // Register handoff rule for CDP over GRE
    // CDP can be encapsulated in GRE with protocol type 0x2000 (Cisco proprietary)
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x2000, "cdp");
}
