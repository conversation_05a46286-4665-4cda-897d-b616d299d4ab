#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "gre"

static
int gre_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
  
  // Check minimum GRE header length (4 bytes)
  if (nxt_mbuf_get_length(mbuf) < 4) {
    printf("GRE: insufficient data length (%d bytes, need at least 4)\n", 
      nxt_mbuf_get_length(mbuf));
      return -1;
    }
    
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);
    // Parse GRE header
    uint16_t flags_version = nxt_mbuf_get_uint16_ntoh(mbuf, 0);
    uint16_t protocol_type = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    uint8_t version = flags_version & 0x07;
    bool checksum_present = (flags_version & 0x8000) != 0;
    bool key_present = (flags_version & 0x2000) != 0;
    bool sequence_present = (flags_version & 0x1000) != 0;

    // Record GRE fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "checksum_present", uinteger, checksum_present ? 1 : 0);
    precord_put(precord, "key_present", uinteger, key_present ? 1 : 0);
    precord_put(precord, "sequence_present", uinteger, sequence_present ? 1 : 0);
    precord_put(precord, "protocol_type", uinteger, protocol_type);

    printf("GRE: Version=%d, Protocol=0x%04x, C=%d, K=%d, S=%d\n", 
           version, protocol_type, checksum_present, key_present, sequence_present);

    int header_len = 4;
    
    // Handle optional fields
    if (checksum_present) {
        header_len += 4; // Checksum + Reserved
    }
    if (key_present) {
        header_len += 4; // Key
    }
    if (sequence_present) {
        header_len += 4; // Sequence Number
    }

    // Set handoff for encapsulated protocol
    nxt_handoff_set_key_of_number(engine, protocol_type);
    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);

    return header_len;
}

static
int gre_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "generic routing encapsulation");
    pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
    pschema_register_field(pschema, "checksum_present", YA_FT_UINT8, "checksum present flag");
    pschema_register_field(pschema, "key_present", YA_FT_UINT8, "key present flag");
    pschema_register_field(pschema, "sequence_present", YA_FT_UINT8, "sequence present flag");
    pschema_register_field_ex(pschema, "protocol_type", YA_FT_UINT16, "protocol type", YA_DISPLAY_BASE_HEX);
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "gre",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = gre_schema_reg,
    .dissectFun   = gre_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(gre)
{
    nxt_dissector_register(&gDissectorDef);
    
    // Register handoff rules for common encapsulated protocols
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x0800, "ipv4");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x86DD, "ipv6");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x0806, "arp");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ipv4", 0x2f, "gre");

    // Register handoff rule for CDP over GRE
    // CDP can be encapsulated in GRE with protocol type 0x2000 (Cisco proprietary)
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gre", 0x2000, "cdp");
}
