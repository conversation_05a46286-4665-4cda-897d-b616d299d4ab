#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <stdbool.h>
#include <stddef.h>
#include <yaBasicUtils/macro.h>
#include <yaFtypes/fvalue.h>
#include <yaProtoRecord/precord.h>

#define PROTO_NAME "cdp"

static int cdp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_,
                       nxt_mbuf_t *mbuf) {

  // Check minimum CDP header length (4 bytes)
  if (nxt_mbuf_get_length(mbuf) < 4) {
    printf("CDP: insufficient data length (%d bytes, need at least 4)\n",
           nxt_mbuf_get_length(mbuf));
    return -1;
  }
  precord_t *precord = nxt_engine_pktzone_get_precord(engine);
  precord_layer_put_new_layer(precord, PROTO_NAME);

  // Parse CDP header
  uint8_t version = nxt_mbuf_get_uint8(mbuf, 0);
  uint8_t ttl = nxt_mbuf_get_uint8(mbuf, 1);
  uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

  // Record CDP fields
  precord_put(precord, "version", uinteger, version);
  precord_put(precord, "ttl", uinteger, ttl);
  precord_put(precord, "checksum", uinteger, checksum);

  printf("CDP: Version=%d, TTL=%d, Checksum=0x%04x\n", version, ttl, checksum);
  nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf,
                         precord);

  return 4; // Basic CDP header length
}

static int cdp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
  pschema_t *pschema = pschema_register_proto(
      db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "cisco discovery protocol");
  pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
  pschema_register_field(pschema, "ttl", YA_FT_UINT8, "time to live");
  pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16,
                            "header checksum", YA_DISPLAY_BASE_HEX);

  return 0;
}

static nxt_dissector_def_t gDissectorDef = {
    .name = "cdp",
    .type = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = cdp_schema_reg,
    .dissectFun = cdp_dissect,
    .handoff = NXT_HANDOFF_DEFAULT,
    .mountAt =
        {
            // CDP uses SNAP encapsulation with Cisco OUI (0x00000C) and type
            // 0x2000
            // We use 0x10000 + 0x2000 = 0x12000 as the key to avoid conflicts
            NXT_MNT_NUMBER("llc", 0x12000),
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(cdp) { nxt_dissector_register(&gDissectorDef); }
