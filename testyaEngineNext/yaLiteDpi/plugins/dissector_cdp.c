#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <stdbool.h>
#include <stddef.h>
#include <yaBasicUtils/macro.h>
#include <yaFtypes/fvalue.h>
#include <yaProtoRecord/precord.h>

#define PROTO_NAME "cdp"

// CDP TLV Types
#define CDP_TLV_DEVICE_ID               0x0001
#define CDP_TLV_ADDRESS                 0x0002
#define CDP_TLV_PORT_ID                 0x0003
#define CDP_TLV_CAPABILITIES            0x0004
#define CDP_TLV_SOFTWARE_VERSION        0x0005
#define CDP_TLV_PLATFORM                0x0006
#define CDP_TLV_IP_PREFIX               0x0007
#define CDP_TLV_VTP_MANAGEMENT_DOMAIN   0x0009
#define CDP_TLV_NATIVE_VLAN             0x000A
#define CDP_TLV_DUPLEX                  0x000B
#define CDP_TLV_APPLIANCE_REPLY         0x000C
#define CDP_TLV_APPLIANCE_QUERY         0x000D
#define CDP_TLV_POWER_CONSUMPTION       0x000E
#define CDP_TLV_MTU                     0x0011
#define CDP_TLV_EXTENDED_TRUST          0x0012
#define CDP_TLV_UNTRUSTED_COS           0x0013
#define CDP_TLV_SYSTEM_NAME             0x0014
#define CDP_TLV_SYSTEM_OBJECT_ID        0x0015
#define CDP_TLV_MANAGEMENT_ADDRESS      0x0016
#define CDP_TLV_LOCATION                0x0017
#define CDP_TLV_EXTERNAL_PORT_ID        0x0018
#define CDP_TLV_POWER_REQUESTED         0x0019
#define CDP_TLV_POWER_AVAILABLE         0x001A
#define CDP_TLV_PORT_UNIDIRECTIONAL     0x001B
#define CDP_TLV_UNKNOWN                 0xFFFF

// CDP Capability flags
#define CDP_CAP_ROUTER          0x01
#define CDP_CAP_TRANSPARENT_BRIDGE  0x02
#define CDP_CAP_SOURCE_ROUTE_BRIDGE 0x04
#define CDP_CAP_SWITCH          0x08
#define CDP_CAP_HOST            0x10
#define CDP_CAP_IGMP_CAPABLE    0x20
#define CDP_CAP_REPEATER        0x40



// TLV parsing context structure
typedef struct {
    nxt_mbuf_t *mbuf;
    precord_t *precord;
    int offset;
    uint16_t type;
    uint16_t length;
} cdp_tlv_context_t;

// Helper function to extract string from TLV data
static bool cdp_extract_string(cdp_tlv_context_t *ctx, char *buffer, size_t buffer_size) {
    if (ctx->length <= 4) {
        return false;
    }

    int str_len = ctx->length - 4;
    if (str_len >= (int)buffer_size) {
        str_len = buffer_size - 1;
    }

    for (int i = 0; i < str_len; i++) {
        buffer[i] = nxt_mbuf_get_uint8(ctx->mbuf, ctx->offset + 4 + i);
    }
    buffer[str_len] = '\0';

    return true;
}

// TLV parsing functions
static void cdp_parse_address(cdp_tlv_context_t *ctx) {
    if (ctx->length < 8) {
        return;
    }

    uint32_t num_addresses = nxt_mbuf_get_uint32_ntoh(ctx->mbuf, ctx->offset + 4);
    int addr_offset = ctx->offset + 8;

    for (uint32_t i = 0; i < num_addresses && i < 5; i++) { // Limit to 5 addresses
        if (addr_offset + 8 > ctx->offset + ctx->length) break;

        uint8_t protocol_type = nxt_mbuf_get_uint8(ctx->mbuf, addr_offset);
        uint8_t protocol_length = nxt_mbuf_get_uint8(ctx->mbuf, addr_offset + 1);
        uint16_t address_length = nxt_mbuf_get_uint16_ntoh(ctx->mbuf, addr_offset + 2 + protocol_length);

        if (protocol_type == 1 && protocol_length == 1 && address_length == 4) {
            // IPv4 address
            uint32_t ip_addr = nxt_mbuf_get_uint32_ntoh(ctx->mbuf, addr_offset + 4 + protocol_length);
            precord_put(ctx->precord, "ip_address", uinteger, ip_addr);
        }

        addr_offset += 4 + protocol_length + address_length;
    }
}

static void cdp_parse_device_id(cdp_tlv_context_t *ctx) {
    char device_id[256];
    if (cdp_extract_string(ctx, device_id, sizeof(device_id))) {
        precord_put(ctx->precord, "deviceid", string, device_id);
    }
}

static void cdp_parse_port_id(cdp_tlv_context_t *ctx) {
    char port_id[256];
    if (cdp_extract_string(ctx, port_id, sizeof(port_id))) {
        precord_put(ctx->precord, "port_id", string, port_id);
    }
}

static void cdp_parse_capabilities(cdp_tlv_context_t *ctx) {
    if (ctx->length < 8) {
        return;
    }

    uint32_t capabilities = nxt_mbuf_get_uint32_ntoh(ctx->mbuf, ctx->offset + 4);
    precord_put(ctx->precord, "capabilities", uinteger, capabilities);
}

static void cdp_parse_software_version(cdp_tlv_context_t *ctx) {
    char soft_ver[512];
    if (cdp_extract_string(ctx, soft_ver, sizeof(soft_ver))) {
        precord_put(ctx->precord, "soft_ver", string, soft_ver);
    }
}

static void cdp_parse_platform(cdp_tlv_context_t *ctx) {
    char platform[256];
    if (cdp_extract_string(ctx, platform, sizeof(platform))) {
        precord_put(ctx->precord, "platform", string, platform);
    }
}

static void cdp_parse_vtp_domain(cdp_tlv_context_t *ctx) {
    char vtp_domain[256];
    if (cdp_extract_string(ctx, vtp_domain, sizeof(vtp_domain))) {
        precord_put(ctx->precord, "vtp_management_domain", string, vtp_domain);
    }
}

static void cdp_parse_system_object_id(cdp_tlv_context_t *ctx) {
    char system_oid[256];
    if (cdp_extract_string(ctx, system_oid, sizeof(system_oid))) {
        precord_put(ctx->precord, "system_object_identifier", string, system_oid);
    }
}

static void cdp_parse_location(cdp_tlv_context_t *ctx) {
    char location[256];
    if (cdp_extract_string(ctx, location, sizeof(location))) {
        precord_put(ctx->precord, "location", string, location);
    }
}

static void cdp_parse_ip_prefix(cdp_tlv_context_t *ctx) {
    if (ctx->length >= 8) {
        uint32_t gateway_addr = nxt_mbuf_get_uint32_ntoh(ctx->mbuf, ctx->offset + 4);
        precord_put(ctx->precord, "odr_default_gateway", uinteger, gateway_addr);
    }
}

// TLV dispatcher function
static void cdp_process_tlv(cdp_tlv_context_t *ctx) {
    switch (ctx->type) {
        case CDP_TLV_DEVICE_ID:
            cdp_parse_device_id(ctx);
            break;
        case CDP_TLV_ADDRESS:
            cdp_parse_address(ctx);
            break;
        case CDP_TLV_PORT_ID:
            cdp_parse_port_id(ctx);
            break;
        case CDP_TLV_CAPABILITIES:
            cdp_parse_capabilities(ctx);
            break;
        case CDP_TLV_SOFTWARE_VERSION:
            cdp_parse_software_version(ctx);
            break;
        case CDP_TLV_PLATFORM:
            cdp_parse_platform(ctx);
            break;
        case CDP_TLV_VTP_MANAGEMENT_DOMAIN:
            cdp_parse_vtp_domain(ctx);
            break;
        case CDP_TLV_SYSTEM_OBJECT_ID:
            cdp_parse_system_object_id(ctx);
            break;
        case CDP_TLV_LOCATION:
            cdp_parse_location(ctx);
            break;
        case CDP_TLV_IP_PREFIX:
            cdp_parse_ip_prefix(ctx);
            break;
        default:
            // Unknown TLV type, skip
            break;
    }
}



static int cdp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_,
                       nxt_mbuf_t *mbuf) {

  // Check minimum CDP header length (4 bytes)
  if (nxt_mbuf_get_length(mbuf) < 4) {
    return NXT_DISSECT_ST_VERIFY_FAILED;
  }

  int packet_length = nxt_mbuf_get_length(mbuf);
  precord_t *precord = nxt_engine_pktzone_get_precord(engine);
  precord_layer_put_new_layer(precord, PROTO_NAME);

  // Parse CDP header
  uint8_t version = nxt_mbuf_get_uint8(mbuf, 0);
  uint8_t ttl = nxt_mbuf_get_uint8(mbuf, 1);
  uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

  // Record CDP header fields
  precord_put(precord, "version", uinteger, version);
  precord_put(precord, "ttl", uinteger, ttl);
  precord_put(precord, "checksum", uinteger, checksum);

  // Parse TLVs
  int offset = 4; // Start after header
  while (offset + 4 <= packet_length) {
    uint16_t tlv_type = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
    uint16_t tlv_length = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 2);

    if (tlv_length < 4 || offset + tlv_length > packet_length) {
      break;
    }

    // Create TLV context and process
    cdp_tlv_context_t ctx = {
        .mbuf = mbuf,
        .precord = precord,
        .offset = offset,
        .type = tlv_type,
        .length = tlv_length
    };

    cdp_process_tlv(&ctx);

    offset += tlv_length;
  }

  nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf,
                         precord);

  return offset;
}

static int cdp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
  pschema_t *pschema = pschema_register_proto(
      db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "cisco discovery protocol");
  // Header fields
  pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
  pschema_register_field(pschema, "ttl", YA_FT_UINT8, "time to live");
  pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16,
                            "header checksum", YA_DISPLAY_BASE_HEX);

  // TLV fields
  pschema_register_field(pschema, "deviceid", YA_FT_STRING, "device identifier");
  pschema_register_field_ex(pschema, "ip_address", YA_FT_UINT32, "IP address", YA_DISPLAY_BASE_HEX);
  pschema_register_field(pschema, "port_id", YA_FT_STRING, "port identifier");
  pschema_register_field(pschema, "soft_ver", YA_FT_STRING, "software version");
  pschema_register_field(pschema, "platform", YA_FT_STRING, "platform");
  pschema_register_field(pschema, "odr_default_gateway", YA_FT_UINT32, "ODR default gateway");
  pschema_register_field(pschema, "vtp_management_domain", YA_FT_STRING, "VTP management domain");
  pschema_register_field_ex(pschema, "capabilities", YA_FT_UINT32, "capabilities", YA_DISPLAY_BASE_HEX);
  pschema_register_field(pschema, "location", YA_FT_STRING, "location");
  pschema_register_field(pschema, "system_object_identifier", YA_FT_STRING, "system object identifier");

  return 0;
}

static nxt_dissector_def_t gDissectorDef = {
    .name = "cdp",
    .type = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = cdp_schema_reg,
    .dissectFun = cdp_dissect,
    .handoff = NXT_HANDOFF_DEFAULT,
    .mountAt =
        {
            NXT_MNT_NUMBER("llc", 0x12000),
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(cdp) { nxt_dissector_register(&gDissectorDef); }
