#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <stdbool.h>
#include <stddef.h>
#include <yaBasicUtils/macro.h>
#include <yaFtypes/fvalue.h>
#include <yaProtoRecord/precord.h>

#define PROTO_NAME "cdp"

// CDP TLV Types
#define CDP_TLV_DEVICE_ID               0x0001
#define CDP_TLV_ADDRESS                 0x0002
#define CDP_TLV_PORT_ID                 0x0003
#define CDP_TLV_CAPABILITIES            0x0004
#define CDP_TLV_SOFTWARE_VERSION        0x0005
#define CDP_TLV_PLATFORM                0x0006
#define CDP_TLV_IP_PREFIX               0x0007
#define CDP_TLV_VTP_MANAGEMENT_DOMAIN   0x0009
#define CDP_TLV_NATIVE_VLAN             0x000A
#define CDP_TLV_DUPLEX                  0x000B
#define CDP_TLV_APPLIANCE_REPLY         0x000C
#define CDP_TLV_APPLIANCE_QUERY         0x000D
#define CDP_TLV_POWER_CONSUMPTION       0x000E
#define CDP_TLV_MTU                     0x0011
#define CDP_TLV_EXTENDED_TRUST          0x0012
#define CDP_TLV_UNTRUSTED_COS           0x0013
#define CDP_TLV_SYSTEM_NAME             0x0014
#define CDP_TLV_SYSTEM_OBJECT_ID        0x0015
#define CDP_TLV_MANAGEMENT_ADDRESS      0x0016
#define CDP_TLV_LOCATION                0x0017
#define CDP_TLV_EXTERNAL_PORT_ID        0x0018
#define CDP_TLV_POWER_REQUESTED         0x0019
#define CDP_TLV_POWER_AVAILABLE         0x001A
#define CDP_TLV_PORT_UNIDIRECTIONAL     0x001B
#define CDP_TLV_UNKNOWN                 0xFFFF

// CDP Capability flags
#define CDP_CAP_ROUTER          0x01
#define CDP_CAP_TRANSPARENT_BRIDGE  0x02
#define CDP_CAP_SOURCE_ROUTE_BRIDGE 0x04
#define CDP_CAP_SWITCH          0x08
#define CDP_CAP_HOST            0x10
#define CDP_CAP_IGMP_CAPABLE    0x20
#define CDP_CAP_REPEATER        0x40

// Helper function to get TLV type name
static const char* cdp_tlv_type_name(uint16_t type) {
    switch (type) {
        case CDP_TLV_DEVICE_ID: return "Device ID";
        case CDP_TLV_ADDRESS: return "Address";
        case CDP_TLV_PORT_ID: return "Port ID";
        case CDP_TLV_CAPABILITIES: return "Capabilities";
        case CDP_TLV_SOFTWARE_VERSION: return "Software Version";
        case CDP_TLV_PLATFORM: return "Platform";
        case CDP_TLV_IP_PREFIX: return "IP Prefix";
        case CDP_TLV_VTP_MANAGEMENT_DOMAIN: return "VTP Management Domain";
        case CDP_TLV_NATIVE_VLAN: return "Native VLAN";
        case CDP_TLV_DUPLEX: return "Duplex";
        case CDP_TLV_APPLIANCE_REPLY: return "Appliance Reply";
        case CDP_TLV_APPLIANCE_QUERY: return "Appliance Query";
        case CDP_TLV_POWER_CONSUMPTION: return "Power Consumption";
        case CDP_TLV_MTU: return "MTU";
        case CDP_TLV_EXTENDED_TRUST: return "Extended Trust";
        case CDP_TLV_UNTRUSTED_COS: return "Untrusted CoS";
        case CDP_TLV_SYSTEM_NAME: return "System Name";
        case CDP_TLV_SYSTEM_OBJECT_ID: return "System Object ID";
        case CDP_TLV_MANAGEMENT_ADDRESS: return "Management Address";
        case CDP_TLV_LOCATION: return "Location";
        case CDP_TLV_EXTERNAL_PORT_ID: return "External Port ID";
        case CDP_TLV_POWER_REQUESTED: return "Power Requested";
        case CDP_TLV_POWER_AVAILABLE: return "Power Available";
        case CDP_TLV_PORT_UNIDIRECTIONAL: return "Port Unidirectional";
        default: return "Unknown";
    }
}

// Helper function to parse capabilities
static void cdp_parse_capabilities(precord_t *precord, uint32_t capabilities) {
    precord_put(precord, "capabilities", uinteger, capabilities);

    // Parse individual capability flags
    if (capabilities & CDP_CAP_ROUTER) {
        printf("CDP: Capability - Router\n");
    }
    if (capabilities & CDP_CAP_TRANSPARENT_BRIDGE) {
        printf("CDP: Capability - Transparent Bridge\n");
    }
    if (capabilities & CDP_CAP_SOURCE_ROUTE_BRIDGE) {
        printf("CDP: Capability - Source Route Bridge\n");
    }
    if (capabilities & CDP_CAP_SWITCH) {
        printf("CDP: Capability - Switch\n");
    }
    if (capabilities & CDP_CAP_HOST) {
        printf("CDP: Capability - Host\n");
    }
    if (capabilities & CDP_CAP_IGMP_CAPABLE) {
        printf("CDP: Capability - IGMP Capable\n");
    }
    if (capabilities & CDP_CAP_REPEATER) {
        printf("CDP: Capability - Repeater\n");
    }
}

// Helper function to parse address TLV
static void cdp_parse_address_tlv(precord_t *precord, nxt_mbuf_t *mbuf, int offset, int length) {
    if (length < 4) {
        printf("CDP: Address TLV too short\n");
        return;
    }

    uint32_t num_addresses = nxt_mbuf_get_uint32_ntoh(mbuf, offset);
    printf("CDP: Number of addresses: %d\n", num_addresses);

    int addr_offset = offset + 4;
    for (uint32_t i = 0; i < num_addresses && i < 5; i++) { // Limit to 5 addresses
        if (addr_offset + 8 > offset + length) break;

        uint8_t protocol_type = nxt_mbuf_get_uint8(mbuf, addr_offset);
        uint8_t protocol_length = nxt_mbuf_get_uint8(mbuf, addr_offset + 1);
        uint16_t address_length = nxt_mbuf_get_uint16_ntoh(mbuf, addr_offset + 2 + protocol_length);

        if (protocol_type == 1 && protocol_length == 1 && address_length == 4) {
            // IPv4 address
            uint32_t ip_addr = nxt_mbuf_get_uint32_ntoh(mbuf, addr_offset + 4 + protocol_length);
            precord_put(precord, "ip_address", uinteger, ip_addr);
            printf("CDP: IPv4 Address: %d.%d.%d.%d\n",
                   (ip_addr >> 24) & 0xFF, (ip_addr >> 16) & 0xFF,
                   (ip_addr >> 8) & 0xFF, ip_addr & 0xFF);
        }

        addr_offset += 4 + protocol_length + address_length;
    }
}

static int cdp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_,
                       nxt_mbuf_t *mbuf) {

  // Check minimum CDP header length (4 bytes)
  if (nxt_mbuf_get_length(mbuf) < 4) {
    printf("CDP: insufficient data length (%d bytes, need at least 4)\n",
           nxt_mbuf_get_length(mbuf));
    return NXT_DISSECT_ST_VERIFY_FAILED;
  }

  int packet_length = nxt_mbuf_get_length(mbuf);
  precord_t *precord = nxt_engine_pktzone_get_precord(engine);
  precord_layer_put_new_layer(precord, PROTO_NAME);

  // Parse CDP header
  uint8_t version = nxt_mbuf_get_uint8(mbuf, 0);
  uint8_t ttl = nxt_mbuf_get_uint8(mbuf, 1);
  uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

  // Record CDP header fields
  precord_put(precord, "version", uinteger, version);
  precord_put(precord, "ttl", uinteger, ttl);
  precord_put(precord, "checksum", uinteger, checksum);

  printf("CDP: Version=%d, TTL=%d, Checksum=0x%04x\n", version, ttl, checksum);

  // Parse TLVs
  int offset = 4; // Start after header
  while (offset + 4 <= packet_length) {
    uint16_t tlv_type = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
    uint16_t tlv_length = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 2);

    if (tlv_length < 4 || offset + tlv_length > packet_length) {
      printf("CDP: Invalid TLV length %d at offset %d\n", tlv_length, offset);
      break;
    }

    printf("CDP: TLV Type=0x%04x (%s), Length=%d\n",
           tlv_type, cdp_tlv_type_name(tlv_type), tlv_length);

    // Parse specific TLV types
    switch (tlv_type) {
      case CDP_TLV_DEVICE_ID:
        if (tlv_length > 4) {
          char device_id[256];
          int str_len = tlv_length - 4;
          if (str_len > 255) str_len = 255;
          for (int i = 0; i < str_len; i++) {
            device_id[i] = nxt_mbuf_get_uint8(mbuf, offset + 4 + i);
          }
          device_id[str_len] = '\0';
          precord_put(precord, "deviceid", string, device_id);
          printf("CDP: Device ID: %s\n", device_id);
        }
        break;

      case CDP_TLV_ADDRESS:
        cdp_parse_address_tlv(precord, mbuf, offset + 4, tlv_length - 4);
        break;

      case CDP_TLV_PORT_ID:
        if (tlv_length > 4) {
          char port_id[256];
          int str_len = tlv_length - 4;
          if (str_len > 255) str_len = 255;
          for (int i = 0; i < str_len; i++) {
            port_id[i] = nxt_mbuf_get_uint8(mbuf, offset + 4 + i);
          }
          port_id[str_len] = '\0';
          precord_put(precord, "port_id", string, port_id);
          printf("CDP: Port ID: %s\n", port_id);
        }
        break;

      case CDP_TLV_CAPABILITIES:
        if (tlv_length >= 8) {
          uint32_t capabilities = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
          cdp_parse_capabilities(precord, capabilities);
        }
        break;

      case CDP_TLV_SOFTWARE_VERSION:
        if (tlv_length > 4) {
          char soft_ver[512];
          int str_len = tlv_length - 4;
          if (str_len > 511) str_len = 511;
          for (int i = 0; i < str_len; i++) {
            soft_ver[i] = nxt_mbuf_get_uint8(mbuf, offset + 4 + i);
          }
          soft_ver[str_len] = '\0';
          precord_put(precord, "soft_ver", string, soft_ver);
          printf("CDP: Software Version: %s\n", soft_ver);
        }
        break;

      case CDP_TLV_PLATFORM:
        if (tlv_length > 4) {
          char platform[256];
          int str_len = tlv_length - 4;
          if (str_len > 255) str_len = 255;
          for (int i = 0; i < str_len; i++) {
            platform[i] = nxt_mbuf_get_uint8(mbuf, offset + 4 + i);
          }
          platform[str_len] = '\0';
          precord_put(precord, "platform", string, platform);
          printf("CDP: Platform: %s\n", platform);
        }
        break;

      case CDP_TLV_VTP_MANAGEMENT_DOMAIN:
        if (tlv_length > 4) {
          char vtp_domain[256];
          int str_len = tlv_length - 4;
          if (str_len > 255) str_len = 255;
          for (int i = 0; i < str_len; i++) {
            vtp_domain[i] = nxt_mbuf_get_uint8(mbuf, offset + 4 + i);
          }
          vtp_domain[str_len] = '\0';
          precord_put(precord, "vtp_management_domain", string, vtp_domain);
          printf("CDP: VTP Management Domain: %s\n", vtp_domain);
        }
        break;

      case CDP_TLV_SYSTEM_OBJECT_ID:
        if (tlv_length > 4) {
          char system_oid[256];
          int str_len = tlv_length - 4;
          if (str_len > 255) str_len = 255;
          for (int i = 0; i < str_len; i++) {
            system_oid[i] = nxt_mbuf_get_uint8(mbuf, offset + 4 + i);
          }
          system_oid[str_len] = '\0';
          precord_put(precord, "system_object_identifier", string, system_oid);
          printf("CDP: System Object ID: %s\n", system_oid);
        }
        break;

      case CDP_TLV_LOCATION:
        if (tlv_length > 4) {
          char location[256];
          int str_len = tlv_length - 4;
          if (str_len > 255) str_len = 255;
          for (int i = 0; i < str_len; i++) {
            location[i] = nxt_mbuf_get_uint8(mbuf, offset + 4 + i);
          }
          location[str_len] = '\0';
          precord_put(precord, "location", string, location);
          printf("CDP: Location: %s\n", location);
        }
        break;

      case CDP_TLV_IP_PREFIX:
        // This TLV can contain ODR default gateway information
        if (tlv_length >= 8) {
          uint32_t gateway_addr = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
          precord_put(precord, "odr_default_gateway", uinteger, gateway_addr);
          printf("CDP: ODR Default Gateway: %d.%d.%d.%d\n",
                 (gateway_addr >> 24) & 0xFF, (gateway_addr >> 16) & 0xFF,
                 (gateway_addr >> 8) & 0xFF, gateway_addr & 0xFF);
        }
        break;

      default:
        printf("CDP: Unhandled TLV type 0x%04x\n", tlv_type);
        break;
    }

    offset += tlv_length;
  }

  nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf,
                         precord);

  return offset;
}

static int cdp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
  pschema_t *pschema = pschema_register_proto(
      db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "cisco discovery protocol");
  // Header fields
  pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
  pschema_register_field(pschema, "ttl", YA_FT_UINT8, "time to live");
  pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16,
                            "header checksum", YA_DISPLAY_BASE_HEX);

  // TLV fields
  pschema_register_field(pschema, "deviceid", YA_FT_STRING, "device identifier");
  pschema_register_field_ex(pschema, "ip_address", YA_FT_UINT32, "IP address", YA_DISPLAY_BASE_HEX);
  pschema_register_field(pschema, "port_id", YA_FT_STRING, "port identifier");
  pschema_register_field(pschema, "soft_ver", YA_FT_STRING, "software version");
  pschema_register_field(pschema, "platform", YA_FT_STRING, "platform");
  pschema_register_field(pschema, "odr_default_gateway", YA_FT_UINT32, "ODR default gateway");
  pschema_register_field(pschema, "vtp_management_domain", YA_FT_STRING, "VTP management domain");
  pschema_register_field_ex(pschema, "capabilities", YA_FT_UINT32, "capabilities", YA_DISPLAY_BASE_HEX);
  pschema_register_field(pschema, "location", YA_FT_STRING, "location");
  pschema_register_field(pschema, "system_object_identifier", YA_FT_STRING, "system object identifier");

  return 0;
}

static nxt_dissector_def_t gDissectorDef = {
    .name = "cdp",
    .type = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = cdp_schema_reg,
    .dissectFun = cdp_dissect,
    .handoff = NXT_HANDOFF_DEFAULT,
    .mountAt =
        {
            NXT_MNT_NUMBER("llc", 0x12000),
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(cdp) { nxt_dissector_register(&gDissectorDef); }
