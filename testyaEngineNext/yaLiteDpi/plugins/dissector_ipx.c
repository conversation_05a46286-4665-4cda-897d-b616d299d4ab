#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "ipx"
#define PPP_PROTOCOL_IPX            0x002B  // Internetwork Packet Exchange

// IPX Packet Types (Novell NetWare)
#define IPX_PACKET_TYPE_NLSP                0x00  // NLSP packets
#define IPX_PACKET_TYPE_ROUTING_INFO        0x01  // RIP packets
#define IPX_PACKET_TYPE_SERVICE_ADVERTISING 0x04  // SAP packets
#define IPX_PACKET_TYPE_SEQUENCED           0x05  // SPX packets
#define IPX_PACKET_TYPE_NCP                 0x11  // NCP packets
#define IPX_PACKET_TYPE_PROPAGATED          0x14  // NetBIOS and other propagated packets

static const char* ipx_packet_type_name(uint8_t packet_type)
{
    switch (packet_type) {
        case IPX_PACKET_TYPE_NLSP:                return "NLSP";
        case IPX_PACKET_TYPE_ROUTING_INFO:        return "Routing Information (RIP)";
        case IPX_PACKET_TYPE_SERVICE_ADVERTISING: return "Service Advertising (SAP)";
        case IPX_PACKET_TYPE_SEQUENCED:           return "Sequenced (SPX)";
        case IPX_PACKET_TYPE_NCP:                 return "NetWare Core Protocol (NCP)";
        case IPX_PACKET_TYPE_PROPAGATED:          return "Propagated (NetBIOS)";
        default:                                  return "Unknown";
    }
}

static
int ipx_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum IPX header length (30 bytes)
    if (nxt_mbuf_get_length(mbuf) < 30) {
        printf("IPX: insufficient data length (%d bytes, need at least 30)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse IPX header (30 bytes total)
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 0);
    uint16_t packet_length = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint8_t transport_control = nxt_mbuf_get_uint8(mbuf, 4);
    uint8_t packet_type = nxt_mbuf_get_uint8(mbuf, 5);
    
    // Destination address (12 bytes: 4 network + 6 node + 2 socket)
    uint32_t dest_network = nxt_mbuf_get_uint32_ntoh(mbuf, 6);
    const uint8_t *dest_node = nxt_mbuf_get_raw(mbuf, 10);
    uint16_t dest_socket = nxt_mbuf_get_uint16_ntoh(mbuf, 16);
    
    // Source address (12 bytes: 4 network + 6 node + 2 socket)
    uint32_t src_network = nxt_mbuf_get_uint32_ntoh(mbuf, 18);
    const uint8_t *src_node = nxt_mbuf_get_raw(mbuf, 22);
    uint16_t src_socket = nxt_mbuf_get_uint16_ntoh(mbuf, 28);

    // Validate packet length
    if (packet_length < 30) {
        printf("IPX: invalid packet length (%d, minimum is 30)\n", packet_length);
        return -1;
    }

    // Record IPX fields
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "packet_length", uinteger, packet_length);
    precord_put(precord, "transport_control", uinteger, transport_control);
    precord_put(precord, "packet_type", uinteger, packet_type);
    precord_put(precord, "packet_type_name", string, ipx_packet_type_name(packet_type));
    
    // Destination address
    precord_put(precord, "dest_network", uinteger, dest_network);
    precord_put(precord, "dest_node", bytes, dest_node, 6);
    precord_put(precord, "dest_socket", uinteger, dest_socket);
    
    // Source address
    precord_put(precord, "src_network", uinteger, src_network);
    precord_put(precord, "src_node", bytes, src_node, 6);
    precord_put(precord, "src_socket", uinteger, src_socket);

    printf("IPX: Checksum=0x%04X, Length=%d, TC=%d, Type=%s (%d)\n", 
           checksum, packet_length, transport_control, 
           ipx_packet_type_name(packet_type), packet_type);
    printf("IPX: Dest=%08X:%02X%02X%02X%02X%02X%02X:%04X, Src=%08X:%02X%02X%02X%02X%02X%02X:%04X\n",
           dest_network, dest_node[0], dest_node[1], dest_node[2], dest_node[3], dest_node[4], dest_node[5], dest_socket,
           src_network, src_node[0], src_node[1], src_node[2], src_node[3], src_node[4], src_node[5], src_socket);

    // Set handoff key based on packet type for next protocol
    nxt_handoff_set_key_of_number(engine, packet_type);
    
    return 30; // IPX header length
}

static
int ipx_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "internetwork packet exchange");
    
    // IPX header fields
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "packet checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "packet_length", YA_FT_UINT16, "packet length");
    pschema_register_field(pschema, "transport_control", YA_FT_UINT8, "transport control (hop count)");
    pschema_register_field(pschema, "packet_type", YA_FT_UINT8, "packet type");
    pschema_register_field(pschema, "packet_type_name", YA_FT_STRING, "packet type name");
    
    // Destination address fields
    pschema_register_field_ex(pschema, "dest_network", YA_FT_UINT32, "destination network", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "dest_node", YA_FT_BYTES, "destination node address");
    pschema_register_field_ex(pschema, "dest_socket", YA_FT_UINT16, "destination socket", YA_DISPLAY_BASE_HEX);
    
    // Source address fields
    pschema_register_field_ex(pschema, "src_network", YA_FT_UINT32, "source network", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "src_node", YA_FT_BYTES, "source node address");
    pschema_register_field_ex(pschema, "src_socket", YA_FT_UINT16, "source socket", YA_DISPLAY_BASE_HEX);
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "ipx",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = ipx_schema_reg,
    .dissectFun   = ipx_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // IPX is mounted via PPP handoff, not directly
        
        NXT_MNT_NUMBER("ppp", PPP_PROTOCOL_IPX),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(ipx)
{
    nxt_dissector_register(&gDissectorDef);
}
