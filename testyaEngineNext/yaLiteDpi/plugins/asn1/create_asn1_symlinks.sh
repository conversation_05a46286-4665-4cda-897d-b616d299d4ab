#!/bin/bash

# <PERSON>ript to create symbolic links for ASN.1 generated files
# This fixes the issue where asn1c generates files with module prefixes
# but the discovery header references them without prefixes

GENERATED_DIR="$1"

if [ -z "$GENERATED_DIR" ] || [ ! -d "$GENERATED_DIR" ]; then
    echo "Usage: $0 <generated_directory>"
    exit 1
fi

cd "$GENERATED_DIR" || exit 1

# Create symbolic links for common ASN.1 types that might have module prefixes
# This handles cases where asn1c generates files like "MODULE_TypeName.h" 
# but the discovery header includes "TypeName.h"

# Function to create symlink if target exists and symlink doesn't exist
create_symlink() {
    local target="$1"
    local link_name="$2"
    
    if [ -f "$target" ] && [ ! -e "$link_name" ]; then
        ln -sf "$target" "$link_name"
        echo "Created symlink: $link_name -> $target"
    fi
}

# Handle IV8 and IV16 types (common in H.235 and H.245)
for prefix in "H235-SECURITY-MESSAGES" "MULTIMEDIA-SYSTEM-CONTROL"; do
    create_symlink "${prefix}_IV8.h" "IV8.h"
    create_symlink "${prefix}_IV16.h" "IV16.h"
    create_symlink "${prefix}_Params.h" "Params.h"
done

# Handle other common types that might have prefixes
# Add more patterns here as needed for other protocols

echo "ASN.1 symlink creation completed for $GENERATED_DIR"
