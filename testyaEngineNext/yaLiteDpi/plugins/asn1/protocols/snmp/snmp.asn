RFC1157-SN<PERSON> DEFINITIONS ::= BEGIN

-- Basic SNMP message structure from RFC 1157
Message ::= SEQUENCE {
    version Version,
    community OCTET STRING,
    data PDUs
}

-- Version definition with all SNMP versions
Version ::= INTEGER { version-1(0), v2c(1), v2u(2), snmpv3(3) }

PDUs ::= CHOICE {
    get-request [0] IMPLICIT PDU,
    get-next-request [1] IMPLICIT PDU,
    get-response [2] IMPLICIT PDU,
    set-request [3] IMPLICIT PDU,
    trap [4] IMPLICIT Trap-PDU,
    getBulkRequest [5] IMPLICIT BulkPDU,
    informRequest [6] IMPLICIT PDU,
    snmpV2-trap [7] IMPLICIT PDU,
    report [8] IMPLICIT PDU
}

PDU ::= SEQUENCE {
    request-id INTEGER (-2147483648..2147483647),
    error-status INTEGER {
        noError(0),
        too<PERSON><PERSON>(1),
        noSuchName(2),      -- for proxy compatibility
        badValue(3),        -- for proxy compatibility
        readOnly(4),        -- for proxy compatibility
        genErr(5),
        noAccess(6),
        wrongType(7),
        wrongLength(8),
        wrongEncoding(9),
        wrongValue(10),
        noCreation(11),
        inconsistentValue(12),
        resourceUnavailable(13),
        commitFailed(14),
        undoFailed(15),
        authorizationError(16),
        notWritable(17),
        inconsistentName(18)
    },
    error-index INTEGER (0..2147483647),
    variable-bindings VarBindList
}

BulkPDU ::= SEQUENCE {
    request-id INTEGER (-2147483648..2147483647),
    non-repeaters INTEGER (0..2147483647),
    max-repetitions INTEGER (0..2147483647),
    variable-bindings VarBindList
}

Trap-PDU ::= [4] IMPLICIT SEQUENCE {
    enterprise OBJECT IDENTIFIER,
    agent-addr NetworkAddress,
    generic-trap INTEGER {
        coldStart(0),
        warmStart(1),
        linkDown(2),
        linkUp(3),
        authenticationFailure(4),
        egpNeighborLoss(5),
        enterpriseSpecific(6)
    },
    specific-trap INTEGER,
    time-stamp TimeTicks,
    variable-bindings VarBindList
}

VarBindList ::= SEQUENCE OF VarBind

VarBind ::= SEQUENCE {
    name ObjectName,
    valueType CHOICE {
        value ObjectSyntax,
        unSpecified NULL,    -- in retrieval requests
                            -- exceptions in responses
        noSuchObject [0] IMPLICIT NULL,
        noSuchInstance [1] IMPLICIT NULL,
        endOfMibView [2] IMPLICIT NULL
    }
}

ObjectName ::= OBJECT IDENTIFIER

ObjectSyntax ::= CHOICE {
    simple SimpleSyntax,
    application-wide ApplicationSyntax
}

SimpleSyntax ::= CHOICE {
    integer-value INTEGER (-2147483648..2147483647),
    string-value OCTET STRING (SIZE (0..65535)),
    objectID-value OBJECT IDENTIFIER
}

ApplicationSyntax ::= CHOICE {
    ipAddress-value IpAddress,
    counter-value Counter32,
    timeticks-value TimeTicks,
    arbitrary-value Opaque,
    big-counter-value Counter64,
    unsigned-integer-value Unsigned32
}

-- Type definitions aligned with RFC 3416 and SMIv2
IpAddress ::= [APPLICATION 0] IMPLICIT OCTET STRING (SIZE (4))
Counter32 ::= [APPLICATION 1] IMPLICIT INTEGER (0..4294967295)
Unsigned32 ::= [APPLICATION 2] IMPLICIT INTEGER (0..4294967295)
Gauge32 ::= Unsigned32
TimeTicks ::= [APPLICATION 3] IMPLICIT INTEGER (0..4294967295)
Opaque ::= [APPLICATION 4] IMPLICIT OCTET STRING
Counter64 ::= [APPLICATION 6] IMPLICIT INTEGER (0..18446744073709551615)

-- Legacy aliases for backward compatibility
NetworkAddress ::= IpAddress
Counter ::= Counter32
Gauge ::= Gauge32

END
