#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "sdx"

static
int sdx_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    uint8_t type = nxt_mbuf_get_uint8(mbuf, 15);
    printf("type = %d\n", type);

    nxt_handoff_set_key_of_number(engine, type);
    return 51;
}

static
int sdx_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "sdx");
    pschema_register_field(pschema,    "sdx",            YA_FT_UINT16,  "sdx");
     return 0;
}

// static
// nxt_dissector_t*  sdx_handoff_find(nxt_handoff_key_t key _U_, nxt_session_t *session _U_, precord_t *precord _U_, void *userdata _U_)
// {

//     nxt_dissector_t *recogProto = NULL;

//     recogProto = nxt_dissector_get_by_name("ipv4");
//     if (NULL == recogProto) {
//         return NULL;
//     }

//     return recogProto;
// }

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "sdx",
    .schemaRegFun = sdx_schema_reg,
    .dissectFun   = sdx_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    // .handoff      = {NXT_HANDOFF_TYPE_CUSTOM, NULL, sdx_handoff_find, NULL, NULL},
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(sdx)
{
    nxt_dissector_register(&gDissectorDef);
    //! ToDo handoff 在此添加
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "sdx", 0x40, "eth");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_PORT_PAYLOAD, "sdx", 0x42, "ipv4");
}
