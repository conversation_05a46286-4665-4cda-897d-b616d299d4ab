开发需求
============

## 已完成
cdp字段
[x]cdp.deviceid
[x]cdp.ip_address
[x]cdp.port_id
[x]cdp.soft_ver
[x]cdp.platform
[x]cdp.odr_default_gateway
[x]cdp.vtp_management_domain
[x]cdp.capabilities
[x]cdp.location
[x]cdp.system_object_identifier

gre字段
[x]protType
[x]callID
[x]seqNum
[x]ackNum
[x]sip
[x]sipCnt
[x]sipAsn
[x]sipCou
[x]dip
[x]dipCnt
[x]dipAsn
[x]dipCou

icmp字段
[x]icmp.data
[x]exc_srcaddr
[x]exc_dstaddr
[x]exc_proto
[x]exc_srcport
[x]exc_dstport
[x]ttl
[x]repTtl
[x]qurType
[x]qurIpv6Addr
[x]qurIpv4Addr
[x]qurDNS
[x]ndpLifeTime
[x]ndpLinkAddr
[x]ndpPreLen
[x]ndpPreFix
[x]ndpValLifeTime
[x]ndpCurMtu
[x]ndpTarAddr
[x]nextHopMtu
[x]excPointer
[x]mulCastAddr
[x]checkSum
[x]checkSumReply
[x]rtraddr
[x]resTime
[x]excTTL
[x]ResponseTime
[x]unreachableSourcePort
[x]unreachableDestinationPort

igmp字段
[x]mulAddr
[x]recType
[x]recNumSrc
[x]recNumMul

isis字段
[x]已有完整实现，无需额外字段

## 开发总结

所有协议字段开发已完成：

- CDP协议：10个字段已实现
- GRE协议：12个字段已实现
- ICMP协议：29个字段已实现
- IGMP协议：4个字段已实现
- ISIS协议：已有完整实现