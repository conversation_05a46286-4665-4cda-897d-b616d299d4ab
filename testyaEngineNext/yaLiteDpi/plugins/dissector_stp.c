#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_util.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>

#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "stp"

// STP BPDU Types
#define STP_BPDU_TYPE_CONFIG        0x00
#define STP_BPDU_TYPE_TCN           0x80

// STP Protocol Identifier and Version
#define STP_PROTOCOL_ID             0x0000
#define STP_PROTOCOL_VERSION        0x00

// STP Flags
#define STP_FLAG_TC                 0x01  // Topology Change
#define STP_FLAG_TCA                0x80  // Topology Change Acknowledgment

// STP Port States
#define STP_PORT_STATE_DISABLED     0
#define STP_PORT_STATE_BLOCKING     1
#define STP_PORT_STATE_LISTENING    2
#define STP_PORT_STATE_LEARNING     3
#define STP_PORT_STATE_FORWARDING   4

static
int stp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum BPDU length (4 bytes for TCN, 35+ bytes for Configuration)
    if (nxt_mbuf_get_length(mbuf) < 4) {
        printf("STP: insufficient data length (%d bytes)\n", nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse common BPDU header
    uint16_t protocol_id = nxt_mbuf_get_uint16_ntoh(mbuf, 0);
    uint8_t version = nxt_mbuf_get_uint8(mbuf, 2);
    uint8_t bpdu_type = nxt_mbuf_get_uint8(mbuf, 3);

    // Validate protocol identifier and version
    if (protocol_id != STP_PROTOCOL_ID) {
        printf("STP: invalid protocol identifier 0x%04x (expected 0x%04x)\n", 
               protocol_id, STP_PROTOCOL_ID);
        return -1;
    }

    if (version != STP_PROTOCOL_VERSION) {
        printf("STP: unsupported version %d (expected %d)\n", version, STP_PROTOCOL_VERSION);
        return -1;
    }

    // Record common fields
    precord_put(precord, "protocol_id", uinteger, protocol_id);
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "bpdu_type", uinteger, bpdu_type);

    printf("STP: Protocol ID=0x%04x Version=%d Type=0x%02x\n", 
           protocol_id, version, bpdu_type);

    int header_len = 4; // Common header length

    if (bpdu_type == STP_BPDU_TYPE_TCN) {
        // Topology Change Notification BPDU (only 4 bytes)
        precord_put(precord, "bpdu_type_name", string, "TCN");
        printf("STP: Topology Change Notification BPDU\n");
        return header_len;
    } else if (bpdu_type == STP_BPDU_TYPE_CONFIG) {
        // Configuration BPDU (35 bytes minimum)
        if (nxt_mbuf_get_length(mbuf) < 35) {
            printf("STP: insufficient data for Configuration BPDU (%d bytes, need 35)\n",
                   nxt_mbuf_get_length(mbuf));
            return -1;
        }

        precord_put(precord, "bpdu_type_name", string, "Configuration");

        // Parse Configuration BPDU fields
        uint8_t flags = nxt_mbuf_get_uint8(mbuf, 4);
        
        // Root Identifier (8 bytes: 2 bytes priority + 6 bytes MAC)
        uint16_t root_priority = nxt_mbuf_get_uint16_ntoh(mbuf, 5);
        uint64_t root_mac = 0;
        for (int i = 0; i < 6; i++) {
            root_mac = (root_mac << 8) | nxt_mbuf_get_uint8(mbuf, 7 + i);
            root_mac = nxt_util_ntoh64(root_mac);
        }
        
        uint32_t root_path_cost = nxt_mbuf_get_uint32_ntoh(mbuf, 13);
        
        // Bridge Identifier (8 bytes: 2 bytes priority + 6 bytes MAC)
        uint16_t bridge_priority = nxt_mbuf_get_uint16_ntoh(mbuf, 17);
        uint64_t bridge_mac = 0;
        for (int i = 0; i < 6; i++) {
            bridge_mac = (bridge_mac << 8) | nxt_mbuf_get_uint8(mbuf, 19 + i);
            bridge_mac = nxt_util_ntoh64(bridge_mac);

        }
        
        uint16_t port_id = nxt_mbuf_get_uint16_ntoh(mbuf, 25);
        uint16_t message_age = nxt_mbuf_get_uint16_ntoh(mbuf, 27);
        uint16_t max_age = nxt_mbuf_get_uint16_ntoh(mbuf, 29);
        uint16_t hello_time = nxt_mbuf_get_uint16_ntoh(mbuf, 31);
        uint16_t forward_delay = nxt_mbuf_get_uint16_ntoh(mbuf, 33);

        // Extract flag bits
        uint8_t tc_flag = flags & STP_FLAG_TC;
        uint8_t tca_flag = (flags & STP_FLAG_TCA) ? 1 : 0;

        // Extract port priority and port number from port_id
        uint8_t port_priority = (port_id >> 8) & 0xFF;
        uint8_t port_number = port_id & 0xFF;

        // Record all fields
        precord_put(precord, "flags", uinteger, flags);
        precord_put(precord, "tc_flag", uinteger, tc_flag);
        precord_put(precord, "tca_flag", uinteger, tca_flag);
        
        precord_put(precord, "root_priority", uinteger, root_priority);
        precord_put(precord, "root_mac", bytes, (const uint8_t *)&root_mac,6);
        precord_put(precord, "root_path_cost", uinteger, root_path_cost);

        precord_put(precord, "bridge_priority", uinteger, bridge_priority);
        precord_put(precord, "bridge_mac", bytes, (const uint8_t *)&bridge_mac,6);
        
        precord_put(precord, "port_id", uinteger, port_id);
        precord_put(precord, "port_priority", uinteger, port_priority);
        precord_put(precord, "port_number", uinteger, port_number);
        
        precord_put(precord, "message_age", uinteger, message_age);
        precord_put(precord, "max_age", uinteger, max_age);
        precord_put(precord, "hello_time", uinteger, hello_time);
        precord_put(precord, "forward_delay", uinteger, forward_delay);

        printf("STP: Configuration BPDU - Root Priority=%d, Bridge Priority=%d, Port ID=0x%04x\n",
               root_priority, bridge_priority, port_id);
        printf("STP: Timers - Hello=%d, MaxAge=%d, FwdDelay=%d, MsgAge=%d\n",
               hello_time, max_age, forward_delay, message_age);
        printf("STP: Flags - TC=%d, TCA=%d\n", tc_flag, tca_flag);

        header_len = 35;
    } else {
        printf("STP: unknown BPDU type 0x%02x\n", bpdu_type);
        precord_put(precord, "bpdu_type_name", string, "Unknown");
        return -1;
    }
    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);

    return header_len;
}

static
int stp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "spanning tree protocol");
    
    // Common BPDU fields
    pschema_register_field_ex(pschema, "protocol_id", YA_FT_UINT16, "protocol identifier", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
    pschema_register_field_ex(pschema, "bpdu_type", YA_FT_UINT8, "BPDU type", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "bpdu_type_name", YA_FT_STRING, "BPDU type name");
    
    // Configuration BPDU specific fields
    pschema_register_field_ex(pschema, "flags", YA_FT_UINT8, "flags", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "tc_flag", YA_FT_UINT8, "topology change flag");
    pschema_register_field(pschema, "tca_flag", YA_FT_UINT8, "topology change acknowledgment flag");
    
    pschema_register_field(pschema, "root_priority", YA_FT_UINT16, "root bridge priority");
    pschema_register_field(pschema, "root_mac", YA_FT_ETHER, "root bridge MAC address");
    pschema_register_field(pschema, "root_path_cost", YA_FT_UINT32, "root path cost");
    
    pschema_register_field(pschema, "bridge_priority", YA_FT_UINT16, "bridge priority");
    pschema_register_field(pschema, "bridge_mac", YA_FT_ETHER, "bridge MAC address");
    
    pschema_register_field_ex(pschema, "port_id", YA_FT_UINT16, "port identifier", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "port_priority", YA_FT_UINT8, "port priority");
    pschema_register_field(pschema, "port_number", YA_FT_UINT8, "port number");
    
    pschema_register_field(pschema, "message_age", YA_FT_UINT16, "message age");
    pschema_register_field(pschema, "max_age", YA_FT_UINT16, "maximum age");
    pschema_register_field(pschema, "hello_time", YA_FT_UINT16, "hello time");
    pschema_register_field(pschema, "forward_delay", YA_FT_UINT16, "forward delay");
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "stp",
    .type         = NXT_DISSECTOR_TYPE_LINK,
    .schemaRegFun = stp_schema_reg,
    .dissectFun   = stp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // STP BPDUs are sent to multicast MAC 01-80-C2-00-00-00
        // They use LLC with DSAP/SSAP = 0x42 (Spanning Tree BPDU)
        // Mount on LLC with DSAP 0x42
        NXT_MNT_NUMBER("llc", 0x42),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(stp)
{
    nxt_dissector_register(&gDissectorDef);
}
