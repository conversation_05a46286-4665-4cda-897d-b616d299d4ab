#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>

static
int rt_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord    = nxt_engine_pktzone_get_precord(engine);
    precord_layer_move_cursor(precord, "4G");

    int offset = 0;
    precord_put(precord, "teid", uinteger, nxt_mbuf_get_uint32_ntoh(mbuf, offset)); offset += 4;

    offset += 4; // skip 4 byte reserved
    precord_put(precord, "outer_sip", uinteger,   nxt_mbuf_get_uint32(mbuf, offset));           offset += 4;
    precord_put(precord, "outer_dip", uinteger,   nxt_mbuf_get_uint32(mbuf, offset));           offset += 4;
    precord_put(precord, "msisdn",    uinteger64, nxt_mbuf_get_uint64_n_ntoh(mbuf, offset, 7)); offset += 7;
    precord_put(precord, "imei",      uinteger64, nxt_mbuf_get_uint64_n_ntoh(mbuf, offset, 7)); offset += 7;
    precord_put(precord, "imsi",      uinteger64, nxt_mbuf_get_uint64_n_ntoh(mbuf, offset, 7)); offset += 7;

    precord_put(precord, "tac",       uinteger, nxt_mbuf_get_uint16_ntoh(mbuf, offset)); offset += 2;
    precord_put(precord, "plmn_id",   uinteger, nxt_mbuf_get_uint16_ntoh(mbuf, offset)); offset += 2;
    precord_put(precord, "uli",       uinteger, nxt_mbuf_get_uint32_ntoh(mbuf, offset)); offset += 4;
    precord_put(precord, "base_type", uinteger, nxt_mbuf_get_uint8(mbuf,  offset));      offset += 1;

    return offset;
}

static
int rt_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db _U_)
{
    pschema_t* schema4G = pschema_register_base(db, "4G", "4G base layer", "#NONE");

    pschema_register_field_ex(schema4G, "teid",      YA_FT_UINT32, "teid",      YA_DISPLAY_BASE_HEX);
    pschema_register_field(schema4G,    "outer_sip", YA_FT_IPv4,   "outer src ip");
    pschema_register_field(schema4G,    "outer_dip", YA_FT_IPv4,   "outer dst ip");
    pschema_register_field(schema4G,    "msisdn",    YA_FT_UINT64, "msisdn");
    pschema_register_field(schema4G,    "imei",      YA_FT_UINT64, "imei");
    pschema_register_field(schema4G,    "imsi",      YA_FT_UINT64, "imsi");
    pschema_register_field_ex(schema4G, "tac",       YA_FT_UINT16, "tac",       YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(schema4G, "plmn_id",   YA_FT_UINT16, "plmn_id",   YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(schema4G, "uli",       YA_FT_UINT32, "uli",       YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(schema4G, "base_type", YA_FT_UINT8,  "base type", YA_DISPLAY_BASE_HEX);

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rt",
    .type         = NXT_DISSECTOR_TYPE_TRAILER,
    .schemaRegFun = rt_schema_reg,
    .dissectFun   = rt_dissect,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rt)
{
    nxt_dissector_register(&gDissectorDef);
}
