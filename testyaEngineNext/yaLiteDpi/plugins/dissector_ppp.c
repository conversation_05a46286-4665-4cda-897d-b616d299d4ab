#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "ppp"

// PPP Frame Format Constants
#define PPP_FLAG                    0x7E
#define PPP_ADDRESS                 0xFF
#define PPP_CONTROL                 0x03

// PPP Protocol Types (RFC 1661 and others)
#define PPP_PROTOCOL_IP             0x0021  // Internet Protocol version 4
#define PPP_PROTOCOL_IPV6           0x0057  // Internet Protocol version 6
#define PPP_PROTOCOL_IPX            0x002B  // Internetwork Packet Exchange
#define PPP_PROTOCOL_APPLETALK      0x0029  // AppleTalk
#define PPP_PROTOCOL_NETBIOS        0x003F  // NetBIOS Frames
#define PPP_PROTOCOL_CISCO          0x0207  // Cisco Discovery Protocol
#define PPP_PROTOCOL_MPLS_UNICAST   0x0281  // MPLS Unicast
#define PPP_PROTOCOL_MPLS_MULTICAST 0x0283  // MPLS Multicast

// PPP Control Protocols
#define PPP_PROTOCOL_LCP            0xC021  // Link Control Protocol
#define PPP_PROTOCOL_PAP            0xC023  // Password Authentication Protocol
#define PPP_PROTOCOL_CHAP           0xC223  // Challenge Handshake Authentication Protocol
#define PPP_PROTOCOL_IPCP           0x8021  // Internet Protocol Control Protocol
#define PPP_PROTOCOL_IPV6CP         0x8057  // IPv6 Control Protocol
#define PPP_PROTOCOL_CCP            0x80FD  // Compression Control Protocol
#define PPP_PROTOCOL_ECP            0x8053  // Encryption Control Protocol

static const char* ppp_protocol_name(uint16_t protocol)
{
    switch (protocol) {
        case PPP_PROTOCOL_IP:             return "IPv4";
        case PPP_PROTOCOL_IPV6:           return "IPv6";
        case PPP_PROTOCOL_IPX:            return "IPX";
        case PPP_PROTOCOL_APPLETALK:      return "AppleTalk";
        case PPP_PROTOCOL_NETBIOS:        return "NetBIOS";
        case PPP_PROTOCOL_CISCO:          return "Cisco Discovery Protocol";
        case PPP_PROTOCOL_MPLS_UNICAST:   return "MPLS Unicast";
        case PPP_PROTOCOL_MPLS_MULTICAST: return "MPLS Multicast";
        case PPP_PROTOCOL_LCP:            return "Link Control Protocol";
        case PPP_PROTOCOL_PAP:            return "Password Authentication Protocol";
        case PPP_PROTOCOL_CHAP:           return "Challenge Handshake Authentication Protocol";
        case PPP_PROTOCOL_IPCP:           return "IP Control Protocol";
        case PPP_PROTOCOL_IPV6CP:         return "IPv6 Control Protocol";
        case PPP_PROTOCOL_CCP:            return "Compression Control Protocol";
        case PPP_PROTOCOL_ECP:            return "Encryption Control Protocol";
        default:                          return "Unknown";
    }
}

static
int ppp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
  
  // Check minimum PPP frame length
  // Minimum: Address(1) + Control(1) + Protocol(1 or 2) = 3 or 4 bytes
  if (nxt_mbuf_get_length(mbuf) < 3) {
    printf("PPP: insufficient data length (%d bytes, need at least 3)\n",
      nxt_mbuf_get_length(mbuf));
      return -1;
    }
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    int offset = 0;
    uint8_t address = 0;
    uint8_t control = 0;
    uint16_t protocol = 0;
    bool address_control_present = true;

    // Check if Address and Control fields are present
    // They might be compressed (omitted) if negotiated during LCP
    uint8_t first_byte = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t second_byte = nxt_mbuf_get_uint8(mbuf, 1);

    if (first_byte == PPP_ADDRESS && second_byte == PPP_CONTROL) {
        // Standard PPP frame with Address and Control fields
        address = first_byte;
        control = second_byte;
        offset = 2;
        address_control_present = true;
    } else {
        // Address and Control fields are compressed (omitted)
        address_control_present = false;
        offset = 0;
    }

    // Parse Protocol field (1 or 2 bytes)
    if (nxt_mbuf_get_length(mbuf) < offset + 1) {
        printf("PPP: insufficient data for protocol field\n");
        return -1;
    }

    uint8_t protocol_first_byte = nxt_mbuf_get_uint8(mbuf, offset);
    
    if ((protocol_first_byte & 0x01) == 0) {
        // Protocol field is 2 bytes (first byte has LSB = 0)
        if (nxt_mbuf_get_length(mbuf) < offset + 2) {
            printf("PPP: insufficient data for 2-byte protocol field\n");
            return -1;
        }
        protocol = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
        offset += 2;
    } else {
        // Protocol field is 1 byte (first byte has LSB = 1)
        protocol = protocol_first_byte;
        offset += 1;
    }

    // Record PPP fields
    if (address_control_present) {
        precord_put(precord, "address", uinteger, address);
        precord_put(precord, "control", uinteger, control);
    }
    precord_put(precord, "address_control_present", uinteger, address_control_present ? 1 : 0);
    precord_put(precord, "protocol", uinteger, protocol);
    precord_put(precord, "protocol_name", string, ppp_protocol_name(protocol));

    printf("PPP: Address/Control=%s, Protocol=0x%04x (%s)\n",
           address_control_present ? "Present" : "Compressed",
           protocol, ppp_protocol_name(protocol));

    // Set handoff key based on protocol
    nxt_handoff_set_key_of_number(engine, protocol);
    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);

    return offset;
}

static
int ppp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "point to point protocol");
    
    pschema_register_field_ex(pschema, "address", YA_FT_UINT8, "address field", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "control", YA_FT_UINT8, "control field", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "address_control_present", YA_FT_UINT8, "address/control fields present");
    pschema_register_field_ex(pschema, "protocol", YA_FT_UINT16, "protocol field", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "protocol_name", YA_FT_STRING, "protocol name");
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "ppp",
    .type         = NXT_DISSECTOR_TYPE_LINK,
    .schemaRegFun = ppp_schema_reg,
    .dissectFun   = ppp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // PPP can be used over various physical layers
        // Common scenarios include:
        // - Serial lines (direct mounting)
        // - PPPoE (Point-to-Point Protocol over Ethernet)
        // - PPPoA (Point-to-Point Protocol over ATM)
        // For now, we'll leave it unmounted and let other protocols register it
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(ppp)
{
    nxt_dissector_register(&gDissectorDef);
    
    // Register handoff rules for common protocols over PPP
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_IP, "ipv4");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_IPV6, "ipv6");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_IPX, "ipx");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_MPLS_UNICAST, "mpls");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_MPLS_MULTICAST, "mpls");

    // Cisco protocols
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_CISCO, "cdp");
    
    // Control protocols
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_LCP, "lcp");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_IPCP, "ipcp");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_IPV6CP, "ipv6cp");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_PAP, "pap");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "ppp", PPP_PROTOCOL_CHAP, "chap");
}
