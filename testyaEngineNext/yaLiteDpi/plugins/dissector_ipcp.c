#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "ipcp"
#define PPP_PROTOCOL_IPCP           0x8021  // Internet Protocol Control Protocol

// IPCP Message Types (RFC 1332)
#define IPCP_CODE_CONFIGURE_REQUEST  1
#define IPCP_CODE_CONFIGURE_ACK      2
#define IPCP_CODE_CONFIGURE_NAK      3
#define IPCP_CODE_CONFIGURE_REJECT   4
#define IPCP_CODE_TERMINATE_REQUEST  5
#define IPCP_CODE_TERMINATE_ACK      6
#define IPCP_CODE_CODE_REJECT        7

// IPCP Configuration Options (RFC 1332)
#define IPCP_OPTION_IP_ADDRESSES     1
#define IPCP_OPTION_IP_COMPRESSION   2
#define IPCP_OPTION_IP_ADDRESS       3
#define IPCP_OPTION_MOBILE_IPv4      4
#define IPCP_OPTION_PRIMARY_DNS      129
#define IPCP_OPTION_PRIMARY_NBNS     130
#define IPCP_OPTION_SECONDARY_DNS    131
#define IPCP_OPTION_SECONDARY_NBNS   132

static const char* ipcp_code_name(uint8_t code)
{
    switch (code) {
        case IPCP_CODE_CONFIGURE_REQUEST:  return "Configure-Request";
        case IPCP_CODE_CONFIGURE_ACK:      return "Configure-Ack";
        case IPCP_CODE_CONFIGURE_NAK:      return "Configure-Nak";
        case IPCP_CODE_CONFIGURE_REJECT:   return "Configure-Reject";
        case IPCP_CODE_TERMINATE_REQUEST:  return "Terminate-Request";
        case IPCP_CODE_TERMINATE_ACK:      return "Terminate-Ack";
        case IPCP_CODE_CODE_REJECT:        return "Code-Reject";
        default:                           return "Unknown";
    }
}

static const char* ipcp_option_name(uint8_t option)
{
    switch (option) {
        case IPCP_OPTION_IP_ADDRESSES:     return "IP-Addresses";
        case IPCP_OPTION_IP_COMPRESSION:   return "IP-Compression-Protocol";
        case IPCP_OPTION_IP_ADDRESS:       return "IP-Address";
        case IPCP_OPTION_MOBILE_IPv4:      return "Mobile-IPv4";
        case IPCP_OPTION_PRIMARY_DNS:      return "Primary-DNS-Server";
        case IPCP_OPTION_PRIMARY_NBNS:     return "Primary-NBNS-Server";
        case IPCP_OPTION_SECONDARY_DNS:    return "Secondary-DNS-Server";
        case IPCP_OPTION_SECONDARY_NBNS:   return "Secondary-NBNS-Server";
        default:                           return "Unknown";
    }
}

static int parse_ipcp_options(nxt_mbuf_t *mbuf, int offset, int options_length, precord_t *precord)
{
    int parsed = 0;
    int option_count = 0;
    
    while (parsed < options_length && option_count < 10) { // Limit to 10 options
        if (nxt_mbuf_get_length(mbuf) < offset + parsed + 2) {
            break;
        }
        
        uint8_t option_type = nxt_mbuf_get_uint8(mbuf, offset + parsed);
        uint8_t option_length = nxt_mbuf_get_uint8(mbuf, offset + parsed + 1);
        
        if (option_length < 2 || parsed + option_length > options_length) {
            break;
        }
        
        printf("IPCP: Option %d - Type=%s (%d), Length=%d\n", 
               option_count, ipcp_option_name(option_type), option_type, option_length);
        
        // Record option information based on option count
        if (option_count == 0) {
            precord_put(precord, "option_0_type", uinteger, option_type);
            precord_put(precord, "option_0_type_name", string, ipcp_option_name(option_type));
            precord_put(precord, "option_0_length", uinteger, option_length);
            
            // Parse option data based on type
            if (option_type == IPCP_OPTION_IP_ADDRESS && option_length == 6) {
                uint32_t ip_address = nxt_mbuf_get_uint32_ntoh(mbuf, offset + parsed + 2);
                precord_put(precord, "option_0_ip_address", uinteger, ip_address);
            } else if ((option_type == IPCP_OPTION_PRIMARY_DNS || 
                       option_type == IPCP_OPTION_SECONDARY_DNS ||
                       option_type == IPCP_OPTION_PRIMARY_NBNS ||
                       option_type == IPCP_OPTION_SECONDARY_NBNS) && option_length == 6) {
                uint32_t server_address = nxt_mbuf_get_uint32_ntoh(mbuf, offset + parsed + 2);
                precord_put(precord, "option_0_server_address", uinteger, server_address);
            }
        } else if (option_count == 1) {
            precord_put(precord, "option_1_type", uinteger, option_type);
            precord_put(precord, "option_1_type_name", string, ipcp_option_name(option_type));
            precord_put(precord, "option_1_length", uinteger, option_length);
            
            if (option_type == IPCP_OPTION_IP_ADDRESS && option_length == 6) {
                uint32_t ip_address = nxt_mbuf_get_uint32_ntoh(mbuf, offset + parsed + 2);
                precord_put(precord, "option_1_ip_address", uinteger, ip_address);
            } else if ((option_type == IPCP_OPTION_PRIMARY_DNS || 
                       option_type == IPCP_OPTION_SECONDARY_DNS ||
                       option_type == IPCP_OPTION_PRIMARY_NBNS ||
                       option_type == IPCP_OPTION_SECONDARY_NBNS) && option_length == 6) {
                uint32_t server_address = nxt_mbuf_get_uint32_ntoh(mbuf, offset + parsed + 2);
                precord_put(precord, "option_1_server_address", uinteger, server_address);
            }
        }
        
        parsed += option_length;
        option_count++;
    }
    
    precord_put(precord, "num_options", uinteger, option_count);
    return parsed;
}

static
int ipcp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum IPCP header length (4 bytes)
    if (nxt_mbuf_get_length(mbuf) < 4) {
        printf("IPCP: insufficient data length (%d bytes, need at least 4)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse IPCP header
    uint8_t code = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t identifier = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    // Record IPCP fields
    precord_put(precord, "code", uinteger, code);
    precord_put(precord, "code_name", string, ipcp_code_name(code));
    precord_put(precord, "identifier", uinteger, identifier);
    precord_put(precord, "length", uinteger, length);

    printf("IPCP: Code=%s (%d), ID=%d, Length=%d\n", 
           ipcp_code_name(code), code, identifier, length);

    int consumed = 4;

    // Parse options for Configure messages
    if ((code == IPCP_CODE_CONFIGURE_REQUEST || 
         code == IPCP_CODE_CONFIGURE_ACK || 
         code == IPCP_CODE_CONFIGURE_NAK || 
         code == IPCP_CODE_CONFIGURE_REJECT) && length > 4) {
        
        int options_length = length - 4;
        if (nxt_mbuf_get_length(mbuf) >= length) {
            parse_ipcp_options(mbuf, 4, options_length, precord);
        }
        consumed = length;
    }

    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);
    return consumed;
}

static
int ipcp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "internet protocol control protocol");
    
    // IPCP header fields
    pschema_register_field(pschema, "code", YA_FT_UINT8, "message code");
    pschema_register_field(pschema, "code_name", YA_FT_STRING, "message code name");
    pschema_register_field(pschema, "identifier", YA_FT_UINT8, "message identifier");
    pschema_register_field(pschema, "length", YA_FT_UINT16, "message length");
    
    // Options
    pschema_register_field(pschema, "num_options", YA_FT_UINT8, "number of options");
    
    // Option 0 fields
    pschema_register_field(pschema, "option_0_type", YA_FT_UINT8, "option 0 type");
    pschema_register_field(pschema, "option_0_type_name", YA_FT_STRING, "option 0 type name");
    pschema_register_field(pschema, "option_0_length", YA_FT_UINT8, "option 0 length");
    pschema_register_field_ex(pschema, "option_0_ip_address", YA_FT_UINT32, "option 0 IP address", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "option_0_server_address", YA_FT_UINT32, "option 0 server address", YA_DISPLAY_BASE_HEX);
    
    // Option 1 fields
    pschema_register_field(pschema, "option_1_type", YA_FT_UINT8, "option 1 type");
    pschema_register_field(pschema, "option_1_type_name", YA_FT_STRING, "option 1 type name");
    pschema_register_field(pschema, "option_1_length", YA_FT_UINT8, "option 1 length");
    pschema_register_field_ex(pschema, "option_1_ip_address", YA_FT_UINT32, "option 1 IP address", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "option_1_server_address", YA_FT_UINT32, "option 1 server address", YA_DISPLAY_BASE_HEX);
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "ipcp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = ipcp_schema_reg,
    .dissectFun   = ipcp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("ppp", PPP_PROTOCOL_IPCP),
        // IPCP is mounted via PPP handoff, not directly
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(ipcp)
{
    nxt_dissector_register(&gDissectorDef);
}
