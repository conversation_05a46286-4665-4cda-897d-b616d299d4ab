#include <yaEngineNext/nxt_parser_core.h>

%%{
    machine fsm;
    alphtype unsigned char;
    include basic "nxt_parser_basic.rl";

    action method      { OUT("method",      parser->s); }
    action version     { OUT("version",     parser->s); }
    action uri         { OUT("uri",         parser->s); }
    action status_code { OUT("status_code", parser->s); }
    action via         { OUT("via",         parser->s); }
    action call_id     { OUT("call_id",     parser->s); }

    INVITEm = 0x49 . 0x4E . 0x56 . 0x49 . 0x54 . 0x45;
    ACKm = 0x41 . 0x43 . 0x4B;
    OPTIONSm = 0x4F . 0x50 . 0x54 . 0x49 . 0x4F . 0x4E . 0x53;
    BYEm = 0x42 . 0x59 . 0x45;
    CANCELm = 0x43 . 0x41 . 0x4E . 0x43 . 0x45 . 0x4C;
    REGISTERm = 0x52 . 0x45 . 0x47 . 0x49 . 0x53 . 0x54 . 0x45 . 0x52;
    ALPHA = 0x41..0x5A | 0x61..0x7A;
    DIGIT = 0x30..0x39;
    alphanum = ALPHA | DIGIT;
    token = (alphanum | "-" | "." | "!" | "%" | "*" | "_" | "+" | "`" | "'" | "~"){1,};
    extension_method = token;
    Method = (INVITEm | ACKm | OPTIONSm | BYEm | CANCELm | REGISTERm | extension_method);
    SP = 0x20;
    Request_URI = (any - (0x22|0x20))+;  # rewritten rule, old define is: Request_URI = SIP_URI | SIPS_URI | absoluteURI;
    SIP_Version = ("SIP" "/" DIGIT{1,} "." DIGIT{1,});
    CR = 0x0D;
    LF = 0x0A;
    CRLF = CR LF;
    Request_Line = Method >mark %method SP Request_URI >mark %uri SP SIP_Version >mark %version CRLF;
    HTAB = 0x09;
    WSP = SP | HTAB;
    LWS = (WSP* CRLF)? WSP{1,};
    SWS = (LWS)?;
    HCOLON = (SP | HTAB)* ":" SWS;
    UTF8_CONT = 0x80..0xBF;
    UTF8_NONASCII = 0xC0..0xDF UTF8_CONT{1} | 0xE0..0xEF UTF8_CONT{2} | 0xF0..0xF7 UTF8_CONT{3} | 0xF8..0xFb UTF8_CONT{4} | 0xFC..0xFD UTF8_CONT{5};
    TEXT_UTF8char = 0x21..0x7E | UTF8_NONASCII;
    header_value = (TEXT_UTF8char | UTF8_CONT | LWS)*;
    Accept = "Accept" HCOLON header_value;
    Accept_Encoding = "Accept-Encoding" HCOLON header_value;
    Accept_Language = "Accept-Language" HCOLON header_value;
    Alert_Info = "Alert-Info" HCOLON header_value;
    Allow = "Allow" HCOLON header_value;
    Authentication_Info = "Authentication-Info" HCOLON header_value;
    Authorization = "Authorization" HCOLON header_value;
    Call_ID = ("Call-ID" | "i") HCOLON header_value >mark %call_id;
    Call_Info = "Call-Info" HCOLON header_value;
    STAR = SWS "*" SWS;
    DQUOTE = 0x22;
    qdtext = LWS | 0x21 | 0x23..0x5B | 0x5D..0x7E | UTF8_NONASCII;
    quoted_pair = "\\" (0x00..0x09 | 0x0B..0x0C | 0x0E..0x7F);
    quoted_string = SWS DQUOTE (qdtext | quoted_pair)* DQUOTE;
    display_name = (token LWS)* | quoted_string;
    LAQUOT = SWS "<";
    SIP_URI = (any - (">"|0x09|0x22|";"|0x20))+;  # rewritten rule, old define is: SIP_URI = "sip:" (userinfo)? hostport uri_parameters (headers)?;
    SIPS_URI = (any - (">"|0x09|0x22|";"|0x20))+;  # rewritten rule, old define is: SIPS_URI = "sips:" (userinfo)? hostport uri_parameters (headers)?;
    absoluteURI = (any - (">"|0x09|0x22|";"|0x20))+;  # rewritten rule, old define is: absoluteURI = scheme ":" (hier_part | opaque_part);
    addr_spec = SIP_URI | SIPS_URI | absoluteURI;
    RAQUOT = ">" SWS;
    name_addr = (display_name)? LAQUOT addr_spec RAQUOT;
    SEMI = SWS ";" SWS;
    EQUAL = SWS "=" SWS;
    qvalue = ("0" ("." DIGIT{0,3})?) | ("1" ("." ("0"){0,3})?);
    c_p_q = "q" EQUAL qvalue;
    delta_seconds = DIGIT{1,};
    c_p_expires = "expires" EQUAL delta_seconds;
    domainlabel = alphanum | alphanum (alphanum | "-")* alphanum;
    toplabel = ALPHA | ALPHA (alphanum | "-")* alphanum;
    hostname = (domainlabel ".")* toplabel (".")?;
    IPv4address = (any - (":"|">"|","|"?"|0x09|"]"|0x22|0x20|"/"|0x0d|";"))+;  # rewritten rule, old define is: IPv4address = DIGIT{1,3} "." DIGIT{1,3} "." DIGIT{1,3} "." DIGIT{1,3};
    IPv6address = (any - (","|0x09|"]"|0x20|0x0d))+;  # rewritten rule, old define is: IPv6address = hexpart (":" IPv4address)?;
    IPv6reference = "[" IPv6address "]";
    host = hostname | IPv4address | IPv6reference;
    gen_value = token | host | quoted_string;
    generic_param = token (EQUAL gen_value)?;
    contact_extension = generic_param;
    contact_params = c_p_q | c_p_expires | contact_extension;
    contact_param = (name_addr | addr_spec) (SEMI contact_params)*;
    COMMA = SWS "," SWS;
    Contact = ("Contact" | "m") HCOLON (STAR | (contact_param (COMMA contact_param)*));
    Content_Disposition = "Content-Disposition" HCOLON header_value;
    Content_Encoding = ("Content-Encoding" | "e") HCOLON header_value;
    Content_Language = "Content-Language" HCOLON header_value;
    Content_Length = ("Content-Length" | "l") HCOLON DIGIT{1,};
    Content_Type = ("Content-Type" | "c") HCOLON header_value;
    CSeq = "CSeq" HCOLON (DIGIT{1,} LWS Method);
    Date = "Date" HCOLON header_value;
    Error_Info = "Error-Info" HCOLON header_value;
    Expires = "Expires" HCOLON header_value;
    tag_param = "tag" EQUAL token;
    from_param = tag_param | generic_param;
    from_spec = (name_addr | addr_spec) (SEMI from_param)*;
    From = ("From" | "f") HCOLON from_spec;
    In_Reply_To = "In-Reply-To" HCOLON header_value;
    Max_Forwards = "Max-Forwards" HCOLON DIGIT{1,};
    MIME_Version = "MIME-Version" HCOLON DIGIT{1,} "." DIGIT{1,};
    Min_Expires = "Min-Expires" HCOLON header_value;
    Organization = "Organization" HCOLON header_value;
    Priority = "Priority" HCOLON header_value;
    Proxy_Authenticate = "Proxy-Authenticate" HCOLON header_value;
    Proxy_Authorization = "Proxy-Authorization" HCOLON header_value;
    Proxy_Require = "Proxy-Require" HCOLON header_value;
    Record_Route = "Record-Route" HCOLON header_value;
    Reply_To = "Reply-To" HCOLON header_value;
    Require = "Require" HCOLON header_value;
    Retry_After = "Retry-After" HCOLON header_value;
    Route = "Route" HCOLON header_value;
    Server = "Server" HCOLON header_value;
    TEXT_UTF8_TRIM = TEXT_UTF8char{1,} (LWS* TEXT_UTF8char)*;
    Subject = ("Subject" | "s") HCOLON (TEXT_UTF8_TRIM)?;
    Supported = ("Supported" | "k") HCOLON header_value;
    Timestamp = "Timestamp" HCOLON header_value;
    to_param = tag_param | generic_param;
    To = ("To" | "t") HCOLON ((name_addr | addr_spec) (SEMI to_param)*);
    Unsupported = "Unsupported" HCOLON header_value;
    User_Agent = "User-Agent" HCOLON header_value;
    protocol_name = "SIP" | token;
    SLASH = SWS "/" SWS;
    protocol_version = token;
    other_transport = token;
    transport = "UDP" | "TCP" | "TLS" | "SCTP" | other_transport;
    sent_protocol = protocol_name SLASH protocol_version SLASH transport;
    COLON = SWS ":" SWS;
    port = DIGIT{1,};
    sent_by = host (COLON port)?;
    ttl = DIGIT{1,3};
    via_ttl = "ttl" EQUAL ttl;
    via_maddr = "maddr" EQUAL host;
    via_received = "received" EQUAL (IPv4address | IPv6address);
    via_branch = "branch" EQUAL token;
    via_extension = generic_param;
    via_params = via_ttl | via_maddr | via_received | via_branch | via_extension;
    via_parm = sent_protocol LWS sent_by (SEMI via_params)*;
    Via = ("Via" | "v") HCOLON (via_parm (COMMA via_parm)*) >mark %via;
    Warning = "Warning" HCOLON header_value;
    WWW_Authenticate = "WWW-Authenticate" HCOLON header_value;
    Allow_Events = "Allow-Events" HCOLON header_value;
    Event = "Event" HCOLON header_value;
    Reason = "Reason" HCOLON header_value;
    Min_SE = "Min-SE" HCOLON header_value;
    RSeq = "RSeq" HCOLON header_value;
    Subscription_State = "Subscription-State" HCOLON header_value;
    Remote_Party_ID = "Remote-Party-ID" HCOLON header_value;
    Cisco_GUID = "Cisco-GUID" HCOLON header_value;
    Nat_Proxy = "Nat-Proxy" HCOLON header_value;
    P_Station_Name = "P-Station-Name" HCOLON header_value;
    P_Hint = "P-Hint" HCOLON header_value;
    P_RTP_Stat = "P-RTP-Stat" HCOLON header_value;
    X_RTP_Stat = "X-RTP-Stat" HCOLON header_value;
    X_Status = "X-Status" HCOLON header_value;
    X_ID = "X-ID" HCOLON header_value;
    X_Web = "X-Web" HCOLON header_value;
    X_Tag = "X-Tag" HCOLON header_value;
    X_Asterisk_HangupCause = "X-Asterisk-HangupCause" HCOLON header_value;
    X_Asterisk_HangupCauseCode = "X-Asterisk-HangupCauseCode" HCOLON header_value;
    X_Replaces = "X-Replaces" HCOLON header_value;
    header_name = token;
    extension_header = header_name HCOLON header_value;
    message_header = (Accept | Accept_Encoding | Accept_Language | Alert_Info | Allow | Authentication_Info | Authorization | Call_ID | Call_Info | Contact | Content_Disposition | Content_Encoding | Content_Language | Content_Length | Content_Type | CSeq | Date | Error_Info | Expires | From | In_Reply_To | Max_Forwards | MIME_Version | Min_Expires | Organization | Priority | Proxy_Authenticate | Proxy_Authorization | Proxy_Require | Record_Route | Reply_To | Require | Retry_After | Route | Server | Subject | Supported | Timestamp | To | Unsupported | User_Agent | Via | Warning | WWW_Authenticate | Allow_Events | Event | Reason | Min_SE | RSeq | Subscription_State | Remote_Party_ID | Cisco_GUID | Nat_Proxy | P_Station_Name | P_Hint | P_RTP_Stat | X_RTP_Stat | X_Status | X_ID | X_Web | X_Tag | X_Asterisk_HangupCause | X_Asterisk_HangupCauseCode | X_Replaces | extension_header) CRLF;
    OCTET = 0x00..0xFF;
    message_body = OCTET*;
    Request = Request_Line (message_header)* CRLF (message_body)?;
    Informational = "100" | "180" | "181" | "182" | "183";
    Redirection = "300" | "301" | "302" | "305" | "380";
    Success = "200";
    Client_Error = "400" | "401" | "402" | "403" | "404" | "405" | "406" | "407" | "408" | "410" | "413" | "414" | "415" | "416" | "420" | "421" | "423" | "480" | "481" | "482" | "483" | "484" | "485" | "486" | "487" | "488" | "491" | "493";
    Server_Error = "500" | "501" | "502" | "503" | "504" | "505" | "513";
    Global_Failure = "600" | "603" | "604" | "606";
    extension_code = DIGIT{3};
    Status_Code = Informational | Redirection | Success | Client_Error | Server_Error | Global_Failure | extension_code;
    reserved = ";" | "/" | "?" | ":" | "@" | "&" | "=" | "+" | "$" | ",";
    mark = "-" | "_" | "." | "!" | "~" | "*" | "'" | "(" | ")";
    unreserved = alphanum | mark;
    HEXDIG = DIGIT | "A" | "B" | "C" | "D" | "E" | "F";
    escaped = "%" HEXDIG HEXDIG;
    Reason_Phrase = (reserved | unreserved | escaped | UTF8_NONASCII | UTF8_CONT | SP | HTAB)*;
    Status_Line = SIP_Version >mark %version SP Status_Code >mark %status_code SP Reason_Phrase CRLF;
    Response = Status_Line (message_header)* CRLF (message_body)?;
    SIP_message = Request | Response;
    main := SIP_message;
}%%

%% write data;

int nxt_parser_sip_init(nxt_parser_t *parser)
{
    int cs = 0;
    %% write init;

    parser->ragelCS = cs;  // 初始化起始状态;
    return 0;
}

int nxt_parser_sip_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata)
{
    // 准备 ragel fsm 需要的变量:cs, p, pe, eof
    int cs             = parser->ragelCS;   // 从 parser 恢复 cs;
    const uint8_t *p   = buff;
    const uint8_t *pe  = buff + len;
    const uint8_t *eof = pe;

    // 执行 parse
    %% write exec;

    // parse 结束，更新状态到 parser 中;
    parser->ragelPoint  = p;
    parser->ragelCS = cs;                   // 将 cs 保存到 parser;

    // 检查解析结果
    if (cs == fsm_error)
    {
      parser->status = NXT_PSTATUS_ERROR;
      return -1;
    }

    if (cs >= fsm_first_final)
    {
      parser->status = NXT_PSTATUS_COMPLETE;
    }
    else
    {
      parser->status = NXT_PSTATUS_PARTIAL;
    }

    // 检测是否需要执行回退操作(例如在末尾时发现一个 token 被 mark 但不没有结束, 发生了截断);
    nxt_parser_check_rollback(parser, p, eof);

    // 计算消耗了多少字节;
    return parser->ragelPoint - buff;
}