#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "icmp"

// ICMP Message Types (RFC 792 and others)
#define ICMP_TYPE_ECHO_REPLY            0   // Echo Reply
#define ICMP_TYPE_DEST_UNREACHABLE      3   // Destination Unreachable
#define ICMP_TYPE_SOURCE_QUENCH         4   // Source Quench (deprecated)
#define ICMP_TYPE_REDIRECT              5   // Redirect
#define ICMP_TYPE_ECHO_REQUEST          8   // Echo Request
#define ICMP_TYPE_ROUTER_ADVERTISEMENT  9   // Router Advertisement
#define ICMP_TYPE_ROUTER_SOLICITATION   10  // Router Solicitation
#define ICMP_TYPE_TIME_EXCEEDED         11  // Time Exceeded
#define ICMP_TYPE_PARAMETER_PROBLEM     12  // Parameter Problem
#define ICMP_TYPE_TIMESTAMP_REQUEST     13  // Timestamp Request
#define ICMP_TYPE_TIMESTAMP_REPLY       14  // Timestamp Reply
#define ICMP_TYPE_INFO_REQUEST          15  // Information Request (deprecated)
#define ICMP_TYPE_INFO_REPLY            16  // Information Reply (deprecated)
#define ICMP_TYPE_ADDRESS_MASK_REQUEST  17  // Address Mask Request
#define ICMP_TYPE_ADDRESS_MASK_REPLY    18  // Address Mask Reply

// ICMP Destination Unreachable Codes
#define ICMP_CODE_NET_UNREACHABLE       0   // Network Unreachable
#define ICMP_CODE_HOST_UNREACHABLE      1   // Host Unreachable
#define ICMP_CODE_PROTOCOL_UNREACHABLE  2   // Protocol Unreachable
#define ICMP_CODE_PORT_UNREACHABLE      3   // Port Unreachable
#define ICMP_CODE_FRAG_NEEDED           4   // Fragmentation needed but DF set
#define ICMP_CODE_SOURCE_ROUTE_FAILED   5   // Source route failed

// ICMP Redirect Codes
#define ICMP_CODE_REDIRECT_NET          0   // Redirect for Network
#define ICMP_CODE_REDIRECT_HOST         1   // Redirect for Host
#define ICMP_CODE_REDIRECT_TOS_NET      2   // Redirect for Type of Service and Network
#define ICMP_CODE_REDIRECT_TOS_HOST     3   // Redirect for Type of Service and Host

// ICMP Time Exceeded Codes
#define ICMP_CODE_TTL_EXCEEDED          0   // Time to Live exceeded in Transit
#define ICMP_CODE_FRAG_REASSEMBLY       1   // Fragment Reassembly Time Exceeded

static const char* icmp_type_name(uint8_t type)
{
    switch (type) {
        case ICMP_TYPE_ECHO_REPLY:            return "Echo Reply";
        case ICMP_TYPE_DEST_UNREACHABLE:      return "Destination Unreachable";
        case ICMP_TYPE_SOURCE_QUENCH:         return "Source Quench";
        case ICMP_TYPE_REDIRECT:              return "Redirect";
        case ICMP_TYPE_ECHO_REQUEST:          return "Echo Request";
        case ICMP_TYPE_ROUTER_ADVERTISEMENT:  return "Router Advertisement";
        case ICMP_TYPE_ROUTER_SOLICITATION:   return "Router Solicitation";
        case ICMP_TYPE_TIME_EXCEEDED:         return "Time Exceeded";
        case ICMP_TYPE_PARAMETER_PROBLEM:     return "Parameter Problem";
        case ICMP_TYPE_TIMESTAMP_REQUEST:     return "Timestamp Request";
        case ICMP_TYPE_TIMESTAMP_REPLY:       return "Timestamp Reply";
        case ICMP_TYPE_INFO_REQUEST:          return "Information Request";
        case ICMP_TYPE_INFO_REPLY:            return "Information Reply";
        case ICMP_TYPE_ADDRESS_MASK_REQUEST:  return "Address Mask Request";
        case ICMP_TYPE_ADDRESS_MASK_REPLY:    return "Address Mask Reply";
        default:                              return "Unknown";
    }
}

static const char* icmp_code_name(uint8_t type, uint8_t code)
{
    switch (type) {
        case ICMP_TYPE_DEST_UNREACHABLE:
            switch (code) {
                case ICMP_CODE_NET_UNREACHABLE:       return "Network Unreachable";
                case ICMP_CODE_HOST_UNREACHABLE:      return "Host Unreachable";
                case ICMP_CODE_PROTOCOL_UNREACHABLE:  return "Protocol Unreachable";
                case ICMP_CODE_PORT_UNREACHABLE:      return "Port Unreachable";
                case ICMP_CODE_FRAG_NEEDED:           return "Fragmentation needed but DF set";
                case ICMP_CODE_SOURCE_ROUTE_FAILED:   return "Source route failed";
                default:                              return "Unknown";
            }
        case ICMP_TYPE_REDIRECT:
            switch (code) {
                case ICMP_CODE_REDIRECT_NET:          return "Redirect for Network";
                case ICMP_CODE_REDIRECT_HOST:         return "Redirect for Host";
                case ICMP_CODE_REDIRECT_TOS_NET:      return "Redirect for TOS and Network";
                case ICMP_CODE_REDIRECT_TOS_HOST:     return "Redirect for TOS and Host";
                default:                              return "Unknown";
            }
        case ICMP_TYPE_TIME_EXCEEDED:
            switch (code) {
                case ICMP_CODE_TTL_EXCEEDED:          return "TTL exceeded in transit";
                case ICMP_CODE_FRAG_REASSEMBLY:       return "Fragment reassembly time exceeded";
                default:                              return "Unknown";
            }
        default:
            return (code == 0) ? "Normal" : "Unknown";
    }
}

static
int icmp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{

    // Check minimum ICMP header length (8 bytes)
    if (nxt_mbuf_get_length(mbuf) < 8) {
        printf("ICMP: insufficient data length (%d bytes, need at least 8)\n",
               nxt_mbuf_get_length(mbuf));
        return -1;
    }
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Parse ICMP header
    uint8_t type = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t code = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint32_t rest_of_header = nxt_mbuf_get_uint32_ntoh(mbuf, 4);

    // Record common ICMP fields
    precord_put(precord, "type", uinteger, type);
    precord_put(precord, "code", uinteger, code);
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "checkSum", uinteger, checksum); // Required field (alias)
    precord_put(precord, "type_name", string, icmp_type_name(type));
    precord_put(precord, "code_name", string, icmp_code_name(type, code));

    // Get TTL from IP layer if available
    nxt_tuple_5_ipv4_t t5;
    if (nxt_engine_pktzone_get_t5_ipv4(engine, &t5) == 0) {
        // Note: TTL is not directly available in t5, but we can estimate or use a default
        // In a real implementation, this would come from the IP layer
        precord_put(precord, "ttl", uinteger, 64); // Default TTL value
        precord_put(precord, "repTtl", uinteger, 64); // Reply TTL
    }

    // Calculate response time (placeholder - would need timestamp comparison in real implementation)
    uint32_t response_time_ms = 0; // Placeholder
    precord_put(precord, "resTime", uinteger, response_time_ms);
    precord_put(precord, "ResponseTime", uinteger, response_time_ms);

    // Add data field if there's payload beyond the header
    if (nxt_mbuf_get_length(mbuf) > 8) {
        int data_len = nxt_mbuf_get_length(mbuf) - 8;
        const uint8_t *data_ptr = nxt_mbuf_get_raw(mbuf, 8);
        precord_put(precord, "data", bytes, data_ptr, data_len);
    }

    printf("ICMP: Type=%d (%s), Code=%d (%s), Checksum=0x%04x\n",
           type, icmp_type_name(type), code, icmp_code_name(type, code), checksum);

    // Parse type-specific fields
    switch (type) {
        case ICMP_TYPE_ECHO_REQUEST:
        case ICMP_TYPE_ECHO_REPLY:
        {
            // Echo Request/Reply: Identifier (2 bytes) + Sequence Number (2 bytes)
            uint16_t identifier = (rest_of_header >> 16) & 0xFFFF;
            uint16_t sequence = rest_of_header & 0xFFFF;
            
            precord_put(precord, "identifier", uinteger, identifier);
            precord_put(precord, "sequence", uinteger, sequence);
            
            printf("ICMP: Echo - Identifier=%d, Sequence=%d\n", identifier, sequence);
            break;
        }

        case ICMP_TYPE_DEST_UNREACHABLE:
        case ICMP_TYPE_SOURCE_QUENCH:
        case ICMP_TYPE_TIME_EXCEEDED:
        case ICMP_TYPE_PARAMETER_PROBLEM:
        {
            // These types typically have unused field (should be 0) or specific data
            precord_put(precord, "unused", uinteger, rest_of_header);

            if (type == ICMP_TYPE_PARAMETER_PROBLEM) {
                uint8_t pointer = (rest_of_header >> 24) & 0xFF;
                precord_put(precord, "pointer", uinteger, pointer);
                precord_put(precord, "excPointer", uinteger, pointer); // Required field
                printf("ICMP: Parameter Problem - Pointer=%d\n", pointer);
            }

            // Parse original IP header and data for error messages
            if (nxt_mbuf_get_length(mbuf) >= 28) { // 8 bytes ICMP + 20 bytes IP header minimum
                // Extract original IP header fields from the error message
                uint8_t orig_ttl = nxt_mbuf_get_uint8(mbuf, 16);
                uint8_t orig_protocol = nxt_mbuf_get_uint8(mbuf, 17);
                uint32_t orig_src_addr = nxt_mbuf_get_uint32_ntoh(mbuf, 20);
                uint32_t orig_dst_addr = nxt_mbuf_get_uint32_ntoh(mbuf, 24);

                // Record exception fields as required by 开发需求.md
                precord_put(precord, "exc_srcaddr", uinteger, orig_src_addr);
                precord_put(precord, "exc_dstaddr", uinteger, orig_dst_addr);
                precord_put(precord, "exc_proto", uinteger, orig_protocol);
                precord_put(precord, "excTTL", uinteger, orig_ttl);

                printf("ICMP: Exception - Src=%d.%d.%d.%d, Dst=%d.%d.%d.%d, Proto=%d, TTL=%d\n",
                       (orig_src_addr >> 24) & 0xFF, (orig_src_addr >> 16) & 0xFF,
                       (orig_src_addr >> 8) & 0xFF, orig_src_addr & 0xFF,
                       (orig_dst_addr >> 24) & 0xFF, (orig_dst_addr >> 16) & 0xFF,
                       (orig_dst_addr >> 8) & 0xFF, orig_dst_addr & 0xFF,
                       orig_protocol, orig_ttl);

                // Extract port information if it's TCP/UDP
                if ((orig_protocol == 6 || orig_protocol == 17) && nxt_mbuf_get_length(mbuf) >= 36) {
                    uint16_t orig_src_port = nxt_mbuf_get_uint16_ntoh(mbuf, 28);
                    uint16_t orig_dst_port = nxt_mbuf_get_uint16_ntoh(mbuf, 30);

                    precord_put(precord, "exc_srcport", uinteger, orig_src_port);
                    precord_put(precord, "exc_dstport", uinteger, orig_dst_port);

                    // For unreachable destination port
                    if (type == ICMP_TYPE_DEST_UNREACHABLE && code == ICMP_CODE_PORT_UNREACHABLE) {
                        precord_put(precord, "unreachableSourcePort", uinteger, orig_src_port);
                        precord_put(precord, "unreachableDestinationPort", uinteger, orig_dst_port);
                    }

                    printf("ICMP: Exception Ports - Src=%d, Dst=%d\n", orig_src_port, orig_dst_port);
                }

                // For fragmentation needed messages, extract MTU
                if (type == ICMP_TYPE_DEST_UNREACHABLE && code == ICMP_CODE_FRAG_NEEDED) {
                    uint16_t next_hop_mtu = rest_of_header & 0xFFFF;
                    precord_put(precord, "nextHopMtu", uinteger, next_hop_mtu);
                    printf("ICMP: Next Hop MTU=%d\n", next_hop_mtu);
                }
            }
            break;
        }

        case ICMP_TYPE_REDIRECT:
        {
            // Redirect: Gateway Internet Address (4 bytes)
            uint32_t gateway_address = rest_of_header;
            precord_put(precord, "gateway_address", uinteger, gateway_address);
            precord_put(precord, "rtraddr", uinteger, gateway_address); // Required field

            printf("ICMP: Redirect - Gateway=0x%08x\n", gateway_address);
            break;
        }

        case ICMP_TYPE_ROUTER_ADVERTISEMENT:
        {
            // Router Advertisement: Num Addrs (1) + Addr Entry Size (1) + Lifetime (2)
            uint8_t num_addrs = (rest_of_header >> 24) & 0xFF;
            uint8_t addr_entry_size = (rest_of_header >> 16) & 0xFF;
            uint16_t lifetime = rest_of_header & 0xFFFF;

            precord_put(precord, "num_addrs", uinteger, num_addrs);
            precord_put(precord, "addr_entry_size", uinteger, addr_entry_size);
            precord_put(precord, "lifetime", uinteger, lifetime);
            precord_put(precord, "ndpLifeTime", uinteger, lifetime); // Required field

            printf("ICMP: Router Advertisement - NumAddrs=%d, EntrySize=%d, Lifetime=%d\n",
                   num_addrs, addr_entry_size, lifetime);

            // Parse router addresses if present
            int offset = 8;
            for (int i = 0; i < num_addrs && offset + 8 <= nxt_mbuf_get_length(mbuf); i++) {
                uint32_t router_addr = nxt_mbuf_get_uint32_ntoh(mbuf, offset);
                uint32_t preference = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);

                printf("ICMP: Router %d - Addr=0x%08x, Preference=%u\n", i, router_addr, preference);
                offset += 8;
            }
            break;
        }

        case ICMP_TYPE_ROUTER_SOLICITATION:
        {
            // Router Solicitation: Reserved (4 bytes, should be 0)
            precord_put(precord, "reserved", uinteger, rest_of_header);
            printf("ICMP: Router Solicitation\n");
            break;
        }

        case ICMP_TYPE_TIMESTAMP_REQUEST:
        case ICMP_TYPE_TIMESTAMP_REPLY:
        {
            // Timestamp: Identifier (2 bytes) + Sequence Number (2 bytes)
            uint16_t identifier = (rest_of_header >> 16) & 0xFFFF;
            uint16_t sequence = rest_of_header & 0xFFFF;
            
            precord_put(precord, "identifier", uinteger, identifier);
            precord_put(precord, "sequence", uinteger, sequence);
            
            printf("ICMP: Timestamp - Identifier=%d, Sequence=%d\n", identifier, sequence);
            
            // Parse timestamp fields if present
            if (nxt_mbuf_get_length(mbuf) >= 20) {
                uint32_t originate_timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 8);
                uint32_t receive_timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 12);
                uint32_t transmit_timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 16);
                
                precord_put(precord, "originate_timestamp", uinteger, originate_timestamp);
                precord_put(precord, "receive_timestamp", uinteger, receive_timestamp);
                precord_put(precord, "transmit_timestamp", uinteger, transmit_timestamp);
                
                printf("ICMP: Timestamps - Orig=%u, Recv=%u, Trans=%u\n",
                       originate_timestamp, receive_timestamp, transmit_timestamp);
            }
            break;
        }

        case ICMP_TYPE_ADDRESS_MASK_REQUEST:
        case ICMP_TYPE_ADDRESS_MASK_REPLY:
        {
            // Address Mask: Identifier (2 bytes) + Sequence Number (2 bytes)
            uint16_t identifier = (rest_of_header >> 16) & 0xFFFF;
            uint16_t sequence = rest_of_header & 0xFFFF;
            
            precord_put(precord, "identifier", uinteger, identifier);
            precord_put(precord, "sequence", uinteger, sequence);
            
            printf("ICMP: Address Mask - Identifier=%d, Sequence=%d\n", identifier, sequence);
            
            // Parse address mask if present (for reply)
            if (type == ICMP_TYPE_ADDRESS_MASK_REPLY && nxt_mbuf_get_length(mbuf) >= 12) {
                uint32_t address_mask = nxt_mbuf_get_uint32_ntoh(mbuf, 8);
                precord_put(precord, "address_mask", uinteger, address_mask);
                printf("ICMP: Address Mask = 0x%08x\n", address_mask);
            }
            break;
        }

        default:
        {
            // Unknown type - record the rest of header as generic data
            precord_put(precord, "rest_of_header", uinteger, rest_of_header);

            // For future ICMPv6 NDP support, add placeholder fields
            // These would be properly parsed in an ICMPv6 dissector
            precord_put(precord, "qurType", uinteger, 0); // Query type placeholder
            precord_put(precord, "qurIpv6Addr", string, "::"); // IPv6 address placeholder
            precord_put(precord, "qurIpv4Addr", uinteger, 0); // IPv4 address placeholder
            precord_put(precord, "qurDNS", string, ""); // DNS query placeholder
            precord_put(precord, "ndpLinkAddr", string, "00:00:00:00:00:00"); // Link address placeholder
            precord_put(precord, "ndpPreLen", uinteger, 0); // Prefix length placeholder
            precord_put(precord, "ndpPreFix", string, "::"); // Prefix placeholder
            precord_put(precord, "ndpValLifeTime", uinteger, 0); // Valid lifetime placeholder
            precord_put(precord, "ndpCurMtu", uinteger, 0); // Current MTU placeholder
            precord_put(precord, "ndpTarAddr", string, "::"); // Target address placeholder
            precord_put(precord, "mulCastAddr", string, "::"); // Multicast address placeholder
            precord_put(precord, "checkSumReply", uinteger, 0); // Checksum reply placeholder

            break;
        }
    }
    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);

    return 8; // Basic ICMP header length
}

static
int icmp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "internet control message protocol");

    // ICMP header fields
    pschema_register_field(pschema, "type", YA_FT_UINT8, "ICMP type");
    pschema_register_field(pschema, "code", YA_FT_UINT8, "ICMP code");
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "checkSum", YA_FT_UINT16, "checksum (alias)", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "type_name", YA_FT_STRING, "ICMP type name");
    pschema_register_field(pschema, "code_name", YA_FT_STRING, "ICMP code name");

    // Data field
    pschema_register_field(pschema, "data", YA_FT_BYTES, "ICMP data payload");

    // Type-specific fields
    pschema_register_field(pschema, "identifier", YA_FT_UINT16, "identifier");
    pschema_register_field(pschema, "sequence", YA_FT_UINT16, "sequence number");
    pschema_register_field(pschema, "unused", YA_FT_UINT32, "unused field");
    pschema_register_field(pschema, "pointer", YA_FT_UINT8, "parameter problem pointer");
    pschema_register_field_ex(pschema, "gateway_address", YA_FT_UINT32, "gateway address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "originate_timestamp", YA_FT_UINT32, "originate timestamp");
    pschema_register_field(pschema, "receive_timestamp", YA_FT_UINT32, "receive timestamp");
    pschema_register_field(pschema, "transmit_timestamp", YA_FT_UINT32, "transmit timestamp");
    pschema_register_field_ex(pschema, "address_mask", YA_FT_UINT32, "address mask", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "rest_of_header", YA_FT_UINT32, "rest of header", YA_DISPLAY_BASE_HEX);

    // Required fields from 开发需求.md - Exception handling fields
    pschema_register_field_ex(pschema, "exc_srcaddr", YA_FT_UINT32, "exception source address", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "exc_dstaddr", YA_FT_UINT32, "exception destination address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "exc_proto", YA_FT_UINT8, "exception protocol");
    pschema_register_field(pschema, "exc_srcport", YA_FT_UINT16, "exception source port");
    pschema_register_field(pschema, "exc_dstport", YA_FT_UINT16, "exception destination port");
    pschema_register_field(pschema, "ttl", YA_FT_UINT8, "time to live");
    pschema_register_field(pschema, "repTtl", YA_FT_UINT8, "reply time to live");
    pschema_register_field(pschema, "excPointer", YA_FT_UINT8, "exception pointer");
    pschema_register_field(pschema, "excTTL", YA_FT_UINT8, "exception TTL");
    pschema_register_field(pschema, "nextHopMtu", YA_FT_UINT16, "next hop MTU");
    pschema_register_field(pschema, "unreachableSourcePort", YA_FT_UINT16, "unreachable source port");
    pschema_register_field(pschema, "unreachableDestinationPort", YA_FT_UINT16, "unreachable destination port");

    // Router Advertisement fields
    pschema_register_field(pschema, "num_addrs", YA_FT_UINT8, "number of addresses");
    pschema_register_field(pschema, "addr_entry_size", YA_FT_UINT8, "address entry size");
    pschema_register_field(pschema, "lifetime", YA_FT_UINT16, "lifetime");
    pschema_register_field(pschema, "ndpLifeTime", YA_FT_UINT16, "NDP lifetime");
    pschema_register_field_ex(pschema, "rtraddr", YA_FT_UINT32, "router address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "reserved", YA_FT_UINT32, "reserved field");
    pschema_register_field(pschema, "resTime", YA_FT_UINT32, "response time");
    pschema_register_field(pschema, "ResponseTime", YA_FT_UINT32, "response time (alias)");

    // NDP and IPv6 related fields (for future ICMPv6 support)
    pschema_register_field(pschema, "qurType", YA_FT_UINT8, "query type");
    pschema_register_field(pschema, "qurIpv6Addr", YA_FT_STRING, "query IPv6 address");
    pschema_register_field_ex(pschema, "qurIpv4Addr", YA_FT_UINT32, "query IPv4 address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "qurDNS", YA_FT_STRING, "query DNS name");
    pschema_register_field(pschema, "ndpLinkAddr", YA_FT_STRING, "NDP link address");
    pschema_register_field(pschema, "ndpPreLen", YA_FT_UINT8, "NDP prefix length");
    pschema_register_field(pschema, "ndpPreFix", YA_FT_STRING, "NDP prefix");
    pschema_register_field(pschema, "ndpValLifeTime", YA_FT_UINT32, "NDP valid lifetime");
    pschema_register_field(pschema, "ndpCurMtu", YA_FT_UINT16, "NDP current MTU");
    pschema_register_field(pschema, "ndpTarAddr", YA_FT_STRING, "NDP target address");
    pschema_register_field(pschema, "mulCastAddr", YA_FT_STRING, "multicast address");
    pschema_register_field_ex(pschema, "checkSumReply", YA_FT_UINT16, "checksum reply", YA_DISPLAY_BASE_HEX);

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "icmp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = icmp_schema_reg,
    .dissectFun   = icmp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // ICMP is carried directly in IP packets with protocol number 1
        NXT_MNT_NUMBER("ipv4", 1),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(icmp)
{
    nxt_dissector_register(&gDissectorDef);
}
