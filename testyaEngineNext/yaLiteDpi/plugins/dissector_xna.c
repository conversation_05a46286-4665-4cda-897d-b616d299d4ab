#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "xna"

// XNS IDP Packet Types (Xerox Network Systems)
#define XNS_PACKET_TYPE_RIP             1   // Routing Information Protocol
#define XNS_PACKET_TYPE_ECHO            2   // Echo Protocol
#define XNS_PACKET_TYPE_ERROR           3   // Error Protocol
#define XNS_PACKET_TYPE_PEP             4   // Packet Exchange Protocol
#define XNS_PACKET_TYPE_SPP             5   // Sequenced Packet Protocol
#define XNS_PACKET_TYPE_BOOT            9   // Boot Protocol

static const char* xns_packet_type_name(uint8_t packet_type)
{
    switch (packet_type) {
        case XNS_PACKET_TYPE_RIP:    return "Routing Information Protocol (RIP)";
        case XNS_PACKET_TYPE_ECHO:   return "Echo Protocol";
        case XNS_PACKET_TYPE_ERROR:  return "Error Protocol";
        case XNS_PACKET_TYPE_PEP:    return "Packet Exchange Protocol (PEP)";
        case XNS_PACKET_TYPE_SPP:    return "Sequenced Packet Protocol (SPP)";
        case XNS_PACKET_TYPE_BOOT:   return "Boot Protocol";
        default:                     return "Unknown";
    }
}

static
int xna_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum XNS IDP header length (30 bytes)
    if (nxt_mbuf_get_length(mbuf) < 30) {
        printf("XNS: insufficient data length (%d bytes, need at least 30)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse XNS IDP header (30 bytes total)
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 0);
    uint16_t packet_length = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint8_t transport_control = nxt_mbuf_get_uint8(mbuf, 4);
    uint8_t packet_type = nxt_mbuf_get_uint8(mbuf, 5);
    
    // Destination address (12 bytes: 4 network + 6 host + 2 socket)
    uint32_t dest_network = nxt_mbuf_get_uint32_ntoh(mbuf, 6);
    const uint8_t *dest_host = nxt_mbuf_get_raw(mbuf, 10);
    uint16_t dest_socket = nxt_mbuf_get_uint16_ntoh(mbuf, 16);
    
    // Source address (12 bytes: 4 network + 6 host + 2 socket)
    uint32_t src_network = nxt_mbuf_get_uint32_ntoh(mbuf, 18);
    const uint8_t *src_host = nxt_mbuf_get_raw(mbuf, 22);
    uint16_t src_socket = nxt_mbuf_get_uint16_ntoh(mbuf, 28);

    // Validate packet length
    if (packet_length < 30) {
        printf("XNS: invalid packet length (%d, minimum is 30)\n", packet_length);
        return -1;
    }

    // Record XNS fields
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "packet_length", uinteger, packet_length);
    precord_put(precord, "transport_control", uinteger, transport_control);
    precord_put(precord, "packet_type", uinteger, packet_type);
    precord_put(precord, "packet_type_name", string, xns_packet_type_name(packet_type));
    
    // Destination address
    precord_put(precord, "dest_network", uinteger, dest_network);
    precord_put(precord, "dest_host", bytes, dest_host, 6);
    precord_put(precord, "dest_socket", uinteger, dest_socket);
    
    // Source address
    precord_put(precord, "src_network", uinteger, src_network);
    precord_put(precord, "src_host", bytes, src_host, 6);
    precord_put(precord, "src_socket", uinteger, src_socket);

    printf("XNS: Checksum=0x%04X, Length=%d, TC=%d, Type=%s (%d)\n", 
           checksum, packet_length, transport_control, 
           xns_packet_type_name(packet_type), packet_type);
    printf("XNS: Dest=%08X:%02X%02X%02X%02X%02X%02X:%04X, Src=%08X:%02X%02X%02X%02X%02X%02X:%04X\n",
           dest_network, dest_host[0], dest_host[1], dest_host[2], dest_host[3], dest_host[4], dest_host[5], dest_socket,
           src_network, src_host[0], src_host[1], src_host[2], src_host[3], src_host[4], src_host[5], src_socket);

    // Set handoff key based on packet type for next protocol
    nxt_handoff_set_key_of_number(engine, packet_type);
    
    return 30; // XNS IDP header length
}

static
int xna_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "xerox network architecture");
    
    // XNS IDP header fields
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "packet checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "packet_length", YA_FT_UINT16, "packet length");
    pschema_register_field(pschema, "transport_control", YA_FT_UINT8, "transport control (hop count)");
    pschema_register_field(pschema, "packet_type", YA_FT_UINT8, "packet type");
    pschema_register_field(pschema, "packet_type_name", YA_FT_STRING, "packet type name");
    
    // Destination address fields
    pschema_register_field_ex(pschema, "dest_network", YA_FT_UINT32, "destination network", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "dest_host", YA_FT_BYTES, "destination host address");
    pschema_register_field_ex(pschema, "dest_socket", YA_FT_UINT16, "destination socket", YA_DISPLAY_BASE_HEX);
    
    // Source address fields
    pschema_register_field_ex(pschema, "src_network", YA_FT_UINT32, "source network", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "src_host", YA_FT_BYTES, "source host address");
    pschema_register_field_ex(pschema, "src_socket", YA_FT_UINT16, "source socket", YA_DISPLAY_BASE_HEX);
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "xna",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = xna_schema_reg,
    .dissectFun   = xna_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // XNA is mounted via LLC handoff, not directly
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(xna)
{
    nxt_dissector_register(&gDissectorDef);
}
