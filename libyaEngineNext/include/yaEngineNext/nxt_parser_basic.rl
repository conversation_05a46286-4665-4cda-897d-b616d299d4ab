%%{
    machine basic;

    action mark      { MARK(parser->s, fcurs);}
    action mark_k    { MARK(parser->k); }
    action mark_v    { MARK(parser->v); }
    action extract_k { EXTRACT(parser->k); }
    action extract_v { EXTRACT(parser->v); }

    action print {
           PRINT(parser->s);
    }

    action error {
        printf("encounter unknown char [%c 0x%x] at %ld, state: %d -> %d\n", fc, fc, p - text, fcurs, ftargs);
   }

    action trace {
        printf("trace: current char is %c, follow str:%s\n", fc, p);
   }
}%%
