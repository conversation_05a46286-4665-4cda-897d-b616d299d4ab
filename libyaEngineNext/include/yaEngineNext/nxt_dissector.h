#ifndef NXT_DISSECTOR_H
#define NXT_DISSECTOR_H

#include "nxt_export.h"
#include <stdint.h>

typedef        uint32_t         nxt_ipv4_t;
typedef struct ProtoRecord      precord_t;
typedef struct ProtoSchemaDB    pschema_db_t;
typedef struct nxt_Engine       nxt_engine_t;
typedef struct nxt_Mbuf         nxt_mbuf_t;
typedef struct nxt_Session      nxt_session_t;
typedef struct nxt_ProtoMessage nxt_pmessage_t;
typedef struct nxt_Dissector    nxt_dissector_t;

/* ======================================================
 * macros
 * ======================================================
 */
#define NXT_MNT_NUMBER(p, num)                            {.proto = p, .key = { .type = NXT_HANDOFF_TYPE_NUMBER, .number = num} }
#define NXT_MNT_PORT_PAYLOAD(p, port, payloadPattern)     {.proto = p, .key = { .type = NXT_HANDOFF_TYPE_PORT_PAYLOAD,                    \
                                                           .portPayload = {.serverPort=port, .payloadData=(uint8_t *)payloadPattern}} }
#define NXT_MNT_END                                       {.proto = 0, .key = { .type = NXT_HANDOFF_TYPE_NONE,   .number = 0 } }
#define NXT_MNT_IS_END(h)                                 (h->proto == 0 && h->key.type == NXT_HANDOFF_TYPE_NONE && h->key.number == 0)
#define NXT_HANDOFF_DEFAULT                               { NXT_HANDOFF_TYPE_NUMBER,  NULL, NULL, NULL, NULL }
#define NXT_HANDOFF_NONE                                  { NXT_HANDOFF_TYPE_NONE,    NULL, NULL, NULL, NULL }

#define NXT_ANY_PORT    0
#define NXT_ANY_PAYLOAD NULL

#ifdef ENGINE_NEXT_CORE
// 对于内置 dissector init 函数需要每个 dissector 拥有唯一的函数名
#  define NXT_DISSECTOR_INIT(proto) void __attribute__((__constructor__)) nxt_dissector_init_##proto (void)
#  define NXT_DISSECTOR_FINI(proto) void                                  nxt_dissector_fini_##proto (void)
#else
// 在 dissector 插件中 dissector 初始化函数统一为 'nxt_dissector_init' 以便动态加载时定位到该函数
#  define NXT_DISSECTOR_INIT(proto) void                                  nxt_dissector_init (void)
#  define NXT_DISSECTOR_FINI(proto) void __attribute__((__destructor__))  nxt_dissector_fini (void)
#endif

/* ======================================================
 * enums
 * ======================================================
 */
typedef enum nxt_dissector_type
{
    NXT_DISSECTOR_TYPE_NONE = 0,
    NXT_DISSECTOR_TYPE_LINK,      // 链路层 dissector
    NXT_DISSECTOR_TYPE_TRAILER,   // trailer dissector
    NXT_DISSECTOR_TYPE_BEARER,    // 承载层，例如 ip, tcp/udp, gtp-u 等;
    NXT_DISSECTOR_TYPE_APP,       // 应用层 dissector
} nxt_dissector_type_enum;

typedef enum nxt_handoff_type
{
    NXT_HANDOFF_TYPE_NONE = 0,
    NXT_HANDOFF_TYPE_NUMBER,
    NXT_HANDOFF_TYPE_PORT_PAYLOAD,
    NXT_HANDOFF_TYPE_CUSTOM,
} nxt_handoff_type_enum;

// TODO: 该 struct 命名不清晰，无法了解用途;
typedef struct nxt_PortPayload
{
    uint16_t  serverPort;
    uint16_t  payloadLen;
    uint8_t  *payloadData;
} nxt_port_payload_t;

typedef struct nxt_HandoffKey
{
    nxt_handoff_type_enum type;

    union
    {
        uint64_t                        number;
        nxt_port_payload_t              portPayload;
        void                           *userdata;
    };
} nxt_handoff_key_t;

/* ======================================================
 * typedefs: callback function types
 * ======================================================
 */
typedef void              (*nxt_cb_dissector_init)(void);
typedef void              (*nxt_cb_dissector_fini)(void);
typedef int               (*nxt_cb_event_handler)(nxt_engine_t *engine, nxt_pmessage_t *message, void *userdata);
typedef int               (*nxt_cb_dissector_schema_reg)(nxt_engine_t *engine, pschema_db_t *db);
typedef int               (*nxt_cb_dissector_dissect)(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf);
typedef void              (*nxt_cb_session_userdata_init)(nxt_session_t *session, void *userdata);
typedef void              (*nxt_cb_session_userdata_finish)(nxt_session_t *session, void *userdata);
typedef int               (*nxt_cb_handoff_register)(nxt_handoff_key_t key, nxt_dissector_t *dissector, void *userdata);
typedef nxt_dissector_t*  (*nxt_cb_handoff_find)(nxt_handoff_key_t key, nxt_session_t *session, precord_t *precord, void *userdata);
typedef void              (*nxt_cb_handoff_on_resolve_done)(void *userdata);

typedef struct nxt_SessionUserdata
{
    uint32_t size;
    nxt_cb_session_userdata_init   initFun;
    nxt_cb_session_userdata_finish finishFun;
} nxt_session_userdata_t;

typedef struct nxt_DissectorHandoff
{
    nxt_handoff_type_enum           type;
    nxt_cb_handoff_register         registerFun;
    nxt_cb_handoff_find             findFun;
    nxt_cb_handoff_on_resolve_done  onResolveDoneFunc;
    void                           *userdata;
} nxt_handoff_t;

typedef struct nxt_HandoffMnt
{
    const char       *proto;
    nxt_handoff_key_t key;
} nxt_handoff_mnt_t;

/*
 * TODO: 该结构能否不导出?
 */
typedef struct nxt_DessectorDef
{
    struct nxt_DessectorDef           *next;         // 连接链表
    const char                        *name;         // dissector 协议名
    nxt_dissector_type_enum            type;         // dissector 类型
    nxt_cb_dissector_schema_reg        schemaRegFun; // schema 注册函数
    nxt_cb_dissector_dissect           dissectFun;   // 解析函数
    nxt_session_userdata_t             userdata;     // 会话 userdata, 用户保存会话状态信息，辅助完成解析;
    nxt_handoff_t                      handoff;      // 本协议如何完成到下一层协议的 handoff
    nxt_handoff_mnt_t                  mountAt[];    // 本协议挂载到某个协议上
} nxt_dissector_def_t;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/*
 * dissector
 */
NXT_EXPORT int              nxt_dissector_register(nxt_dissector_def_t *dissectorDef);
NXT_EXPORT int              nxt_dissector_unregister(nxt_dissector_def_t *dissectorDef);
NXT_EXPORT int              nxt_dissector_load_builtin();
NXT_EXPORT uint32_t         nxt_dissector_get_count();
NXT_EXPORT nxt_dissector_t *nxt_dissector_get_by_name(const char *name);
NXT_EXPORT nxt_dissector_t *nxt_dissector_get_by_index(uint32_t index);
NXT_EXPORT int              nxt_dissector_is_type_of(const char *dissectorName, nxt_dissector_type_enum type);
NXT_EXPORT int              nxt_dissector_do_dissect(nxt_engine_t *engine, nxt_dissector_t* dissector, nxt_session_t *session, nxt_mbuf_t *mbuf);

/**
 * @brief 增加 handoff 规则
 *
 *  为解析器添加新的 handoff 规则，根据不同 handoff type 提供相应的处理方式
 *
 * @param type: handoff 类型 在 nxt_handoff_type_enum 枚举中
 * @param dissectorName: 父解析器名称
 * @param ...: handoff 参数，根据 type 决定不同的参数
 *       - type: NXT_HANDOFF_TYPE_NUMBER：
 *         - int number
 *         - const char * childDissectorName 子解析器名称
 *       - type: NXT_HANDOFF_TYPE_PORT_PAYLOAD：
 *         - int port
 *         - char * payload
 *         - const char * childDissectorName
 * @code{.cpp}
 *  nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gtp_u", 0xff, "ipv4");
 *  nxt_handoff_new_rule(NXT_HANDOFF_TYPE_PORT_PAYLOAD, "tcp", 8080, "hello", "rtsp");
 * @endcode
 */
NXT_EXPORT int              nxt_handoff_new_rule(int type, const char * dissectorName, ...);

#ifdef __cplusplus
}
#endif

#endif /* NXT_DISSECTOR_H */
