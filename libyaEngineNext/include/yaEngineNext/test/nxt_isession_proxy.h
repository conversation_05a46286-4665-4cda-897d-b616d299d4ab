#include <yaBasicUtils/macro.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_mbuf.h>

/**
 * @brief nxt_session_t 对象代理接口
 *
 * 定义虚函数接口，用于代理 nxt_session_t 的核心功能，支持自定义实现。
 * 主要用于单元测试场景，可与 Google Test 框架结合使用，通过 Mock 技术实现行为模拟。
 *
 * @note 所有虚函数默认实现为空操作，返回0，继承类需根据需要重写这些方法
 *
 * @example
 * 在 Google Test 中使用示例：
 * @code{.cpp}
 * class MockSessionProxy : public nxt_ISessionProxy
 * {
 * public:
 *     MOCK_METHOD(int, readToRingbuf, (nxt_direction_enum direction, nxt_ringbuf_t *rbuf,
 *                                     uint32_t read_len, nxt_stream_read_res_t *read_status), (override));
 *
 *     MOCK_METHOD(int, onEvent, (nxt_event event, nxt_mbuf_t *mbuf, precord_t *precord), (override));
 * };
 * @endcode
 */
struct nxt_ISessionProxy
{
public:
    virtual ~nxt_ISessionProxy() {};

public:
    virtual int readToRingbuf(nxt_direction_enum direction _U_, nxt_ringbuf_t *rbuf _U_, uint32_t readLen _U_, nxt_stream_read_res_t *readStatus _U_) { return 0;};
    virtual int onEvent( nxt_event event _U_, nxt_mbuf_t *mbuf _U_, precord_t *precord _U_) { return 0; };
};