#ifndef NXT_RECOGNIZER_H
#define NXT_RECOGNIZER_H

#include "nxt_export.h"
#include <stdint.h>

typedef struct nxt_Dissector       nxt_dissector_t;
typedef struct nxt_ProtoRecognizer nxt_recognizer_t;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

NXT_EXPORT nxt_recognizer_t* nxt_recog_create();
NXT_EXPORT void              nxt_recog_destroy(nxt_recognizer_t* recog);
NXT_EXPORT int               nxt_recog_register(nxt_recognizer_t* recog, uint16_t port, const char *pattern, nxt_dissector_t *dissector);
NXT_EXPORT nxt_dissector_t*  nxt_recog_do_recog(nxt_recognizer_t* recog, uint16_t port, uint8_t *payload, uint16_t payloadLen);
NXT_EXPORT int               nxt_recog_register_done(nxt_recognizer_t* recog);

#ifdef __cplusplus
}
#endif

#endif /* NXT_RECOGNIZER_H */
