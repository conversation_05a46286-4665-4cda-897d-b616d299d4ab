#ifndef NXT_RINGBUFF_H
#define NXT_RINGBUFF_H

#include "nxt_export.h"
#include <yaBasicUtils/allocator.h>
#include <stdint.h>

typedef struct nxt_Ringbuf nxt_ringbuf_t;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

NXT_EXPORT nxt_ringbuf_t* nxt_ringbuf_create_wa(ya_allocator_t *alloc, uint32_t size); // With Allocator
NXT_EXPORT void           nxt_ringbuf_destroy_wa(ya_allocator_t *alloc, nxt_ringbuf_t *rbuf);
NXT_EXPORT int            nxt_ringbuf_push_back(nxt_ringbuf_t *rbuf, const uint8_t *from, uint32_t len);
NXT_EXPORT int            nxt_ringbuf_pop_front(nxt_ringbuf_t *rbuf, uint32_t len);
NXT_EXPORT uint32_t       nxt_ringbuf_get_free_size(nxt_ringbuf_t *rbuf);
NXT_EXPORT uint32_t       nxt_ringbuf_get_data_length(nxt_ringbuf_t *rbuf);
NXT_EXPORT uint8_t*       nxt_ringbuf_get_data(nxt_ringbuf_t *rbuf);

// 以下接口被废弃，将使用 wa 版本;
NXT_EXPORT nxt_ringbuf_t* nxt_ringbuf_create(uint32_t size)        __attribute__((deprecated));
NXT_EXPORT void           nxt_ringbuf_destroy(nxt_ringbuf_t *rbuf) __attribute__((deprecated));

#ifdef __cplusplus
}
#endif

#endif /* NXT_RINGBUFF_H */
