#ifndef NXT_MBUF_H
#define NXT_MBUF_H

#include "nxt_export.h"
#include <yaBasicUtils/allocator.h>
#include <stdint.h>

typedef struct nxt_Mbuf  nxt_mbuf_t;

typedef enum nxt_mbuf_seek
{
    NXT_MBUF_SEEK_SET = 0,
    NXT_MBUF_SEEK_CUR = 1,
    NXT_MBUF_SEEK_END = 2,
} nxt_mbuf_seek_enum;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct nxt_MbufRange
{
    int begin;
    int end;
} nxt_mbuf_range_t;

// mbuf create and destroy(wa version)
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_blank_wa(ya_allocator_t* alloc, const uint32_t buffSize);
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_by_copy_wa(ya_allocator_t* alloc, const uint8_t *data, uint32_t dataLen);
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_by_ref_wa(ya_allocator_t* alloc, const uint8_t *data, uint32_t dataLen);
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_clone_wa(ya_allocator_t* alloc, nxt_mbuf_t *mbuf);
NXT_EXPORT void           nxt_mbuf_free_wa(ya_allocator_t *alloc, nxt_mbuf_t *mbuf);

// mbuf write
NXT_EXPORT int            nxt_mbuf_memcpy_in(nxt_mbuf_t *mbuf, int offset, const uint8_t *bytes, uint32_t len);
NXT_EXPORT int            nxt_mbuf_strcpy_in(nxt_mbuf_t *mbuf, int offset, const char *str);
NXT_EXPORT int            nxt_mbuf_strncpy_in(nxt_mbuf_t *mbuf, int offset, const char *str, uint32_t len);

// mbuf get length
NXT_EXPORT int            nxt_mbuf_get_length(nxt_mbuf_t *mbuf);
NXT_EXPORT int            nxt_mbuf_get_capacity(nxt_mbuf_t *mbuf);

// get bytes
#define                   NXT_MBUF_MTOD(mbuf, offset, type) ((type *)nxt_mbuf_get_bytes(mbuf, offset, sizeof (type)))
NXT_EXPORT const uint8_t* nxt_mbuf_get_raw(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT const uint8_t* nxt_mbuf_get_bytes(nxt_mbuf_t *mbuf, uint32_t offset, uint32_t len);
NXT_EXPORT const char*    nxt_mbuf_get_string(nxt_mbuf_t *mbuf, uint32_t offset);

// mbuf seek
NXT_EXPORT nxt_mbuf_range_t  nxt_mbuf_range_tell(nxt_mbuf_t *mbuf);
NXT_EXPORT nxt_mbuf_range_t  nxt_mbuf_range_set(nxt_mbuf_t *mbuf, int begin, int end);
NXT_EXPORT nxt_mbuf_range_t  nxt_mbuf_range_reset(nxt_mbuf_t *mbuf);
NXT_EXPORT nxt_mbuf_range_t  nxt_mbuf_range_adjust(nxt_mbuf_t *mbuf, int beginDiff, int endDiff);

/*
 * nxt_mbuf get integer
 */
NXT_EXPORT int8_t         nxt_mbuf_get_int8(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT int16_t        nxt_mbuf_get_int16(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT int32_t        nxt_mbuf_get_int32(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT int64_t        nxt_mbuf_get_int64(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT int64_t        nxt_mbuf_get_int64_n(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len);
NXT_EXPORT int16_t        nxt_mbuf_get_int16_ntoh(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT int32_t        nxt_mbuf_get_int32_ntoh(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT int64_t        nxt_mbuf_get_int64_ntoh(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT int64_t        nxt_mbuf_get_int64_n_ntoh(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len);

/*
 * nxt_mbuf get unsigned integer
 */
NXT_EXPORT uint8_t        nxt_mbuf_get_uint8(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT uint16_t       nxt_mbuf_get_uint16(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT uint32_t       nxt_mbuf_get_uint32(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT uint64_t       nxt_mbuf_get_uint64(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT uint64_t       nxt_mbuf_get_uint64_n(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len);
NXT_EXPORT uint16_t       nxt_mbuf_get_uint16_ntoh(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT uint32_t       nxt_mbuf_get_uint32_ntoh(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT uint64_t       nxt_mbuf_get_uint64_ntoh(nxt_mbuf_t *mbuf, uint32_t offset);
NXT_EXPORT uint64_t       nxt_mbuf_get_uint64_n_ntoh(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len);

// 以下接口被废弃，将使用 wa 版本;
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_blank(const uint32_t buffSize)                 __attribute__((deprecated));
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_by_copy(const uint8_t *data, uint32_t dataLen) __attribute__((deprecated));
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_by_ref(const uint8_t *data, uint32_t dataLen)  __attribute__((deprecated));
NXT_EXPORT nxt_mbuf_t*    nxt_mbuf_new_clone(nxt_mbuf_t *mbuf)                        __attribute__((deprecated));
NXT_EXPORT void           nxt_mbuf_free(nxt_mbuf_t *mbuf)                             __attribute__((deprecated));

#ifdef __cplusplus
}
#endif

#endif /* NXT_MBUF_H */
