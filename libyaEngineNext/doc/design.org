#+TITLE: engineNext-design
#+AUTHOR: zhengsw
#+DATE: 2023.10.11

* 设计目标
  - [ ] 如何实现 sdt 根据 record 匹配命中之后，决定要 dump 完整会话流量? 默认缓存前 n 包，递交 record 之后必须主动要求继续缓存,
        后续通过 PE_PACKET_ARRIVE 事件继续通知应用层;
  - [ ] 输出结果不限于对 record 进行 log, 还支持对会话 packet 的持续处理，例如将某会话的 packet 进行 dump 或者 forward;
  - [ ] 支持 tcp 流重组, 支持前 n 包重组(如 ssl 只有前 n 包数据可解析); 但默认不重组，需要基于 tcp 的 dissector 主动要求;

* 限制
  - 不支持 tcp 残缺会话重组，判定为残缺时终止会话;
  - 一个协议目标仅支持注册一个 eventHandler;
  - dissector 必须在首个 PE_MESSAGE_ARRIVE 事件时决定是否需要 packet 数据，否则默认不进行包缓存(不是tcp 重组缓存);
  - 不内置支持 tcp 协议消息分割器，需要由 dissector 自己实现; 使用时类似 socket 的方式;

* 测试用例
  - [ ] 支持 rtsp 重组解析，输出;
  - [ ] 支持 ssl 解析前 k 包，并将会话 dump 为 pcap(考虑有 sdx 头，或者 trailer 场景);
  - [ ] 支持 wxf 重组解析，并将会话 dump 为 pcap;
  - [ ] 支持 http 重组解析，输出;

* 设计原则
** 命名规范
*** 接口
   - 导出为 c 接口，不导出任何 c++ 语法元素;
   - 接口与 c 源文件命名统一为 'engineNext_xxx', xxx 部分为小骆驼风格;
   - 头文件 /usr/include/local/yaEngineNext/engineNext.h
   - 函数命名: 统一为 'nxt_module_function' 形式，如 nxt_mbuf_init, nxt_dissector_load 等;
*** 实现
    - 导出类(会出现在接口中的数据类型, 不暴露实现)，类名统一为 nxt_Widget(后续考虑名字空间) 形式，'nxt_' 后面采用大骆驼风格, 类对应的文件名与类名一致，
      例如 nxt_MessageBuff 类位于 MessageBuff.h 中;
    - 内部实现类定义在名字空间 nxt 中;
    - 其它非类形式源文件命令为 c 风格 'engineNext_xxx', 如 engineNext_api.cpp;
    - dissector 插件源文件命令统一为 'nxt_dissector_xxx.c', 如 nxt_dissector_ipv4.c
    - dissector 插件 so 文件命令统一为 'libyaNxtDissector_xxx.so', 如 libyaNxtDissector_ipv4.so

** mbuf 管理
  - [ ] 关于 int nxt_engine_run(nxt_engine_t *engine, nxt_mbuf_t *mbuf) 接口上的 mbuf 的内存管理问题;
        暂时实现为 copy 进去的方式，内部自己管理自己的 nxt_mbuf 的;
  - [ ] mbuf 拥有 data; init, clone, free 分别会进行 malloc, memcpy, 与 free 操作;
  - [ ] mbuf 的创建与销毁由 engine 负责，它通过寄存器进行暂存，之后交给重组队列，出队也是交给寄存器，
        这些寄存器在一次 engine 解析执行完成之后需要进行清理;
  - [ ] nxt_engine_run 接口总是将输入数据 copy 一份到自己内部，由它自己在 mbuf 被 use 之后进行销毁，可能的时机有:
    - 参与完成解析, engineRun 的末尾;
    - 从 packet 缓存队列中被出队，使用;
    - 可能被 reassemble mbuf 引用，reassemble 队列出队;
  - [X] mbuf 需要有便利的创建方法，支持 owner 与 visitor 模式来管理内存; 分别命名为 copy_from 与 refer_to;
  - [X] mbuf 提供 buffer 写入接口，将其设计为一个大小为 size 的 buffer, 使用 offset_begin 与 offset_end 标记一段 message;
  - [X] mbuf 提供安全(带范围检查的 api): 添加 nxt_mbuf_append, nxt_mbuf_get_at, nxt_mbuf_set_at 接口(支持负向 index);
  - [X] mbuf 支持充当 string buffer;
  - [X] mbuf 可以提供 view，或者是 refer 功能，这个 mbuf 指向了其它 mbuf 的内存而已，目的是我们可以自由的进行 advance, 但不能进行写入;
  - [X] 尽量阻止拿到底层的 bytes 内存块，避免发生读取越界的情况，这要求 mbuf 提供了便利的访问方法；

*** mbuf 与 mbuf_view
    - 在 mbuf 上使用 mbuf_view 限定一个 range, 给每个 dissector 时它只能看到应该由它处理的那一层 layer 部分 mbuf; 为了便利，也为了安全;
    - mbuf_view 目前是构建了一个新的 mbuf 对象，这会有构造与析构的开销，为此调整为 push-pop 方式在原 mbuf 上调整 base, read_cursor, write_cursor 指针;
    - mbuf 交给 dissector 时是调整了 base, read_cursor, write_cursor 指针的，但回到框架时这些都会恢复原状;
    - dissector 看到的 mbuf 是被调整了 base, read_cursor, write_cursor 的，但它试图 clone mbuf 时得到的新的 mbuf 同样 clone 了这些指针; 这些 mbuf 只要不
      进行写操作不会影响到原 mbuf; 这些 dissector 可以通过 reset_cursor 将 mbuf 指针进行归位，从而得到 mbuf 的原始状态(例如进行 dump packet 等);
*** mbuf 移除 cursor, 只提供 base 的变化, 不记录 read_cursor 与 write_cursor;
    - mbuf 的实际最大可读写范围为 [buff, buff + size) 表示;
    - mbuf 可读写的范围由 [buff + base, buff + len) 表示; 或者叫 window; base 默认为 0, len 默认为 size;
    - 提供 mbuf_window_get(mbuf, mbuf_window *window), 以及 mbuf_window_adjust(mbuf, mbuf_window *window) 来进行调整 window 的位置与大小, 使用绝对位置进行调整;
    - mbuf[i] 是在可读写范围内进行读写;

*** mbuf 的生命周期管理原则
  - 谁创建谁负责销毁;
  - clone 也是一种创建操作，需要被销毁;
  - mbuf 也是一种创建操作，需要被销毁;
  - mbuf 应该谁创建谁负责销毁，但是系统中会存在希望'暂存 mbuf' 的场景，例如 tcp 重组，包缓存等，这些都需要 clone, 但我们可以使用引用计数来实现 clone;

** dissector
   - dissector 以模块/插件形式存在，被 engine 加载; 添加一个 dissector 模块就是增加了一个 dissector_xx.c 文件，
     移除该 dissector 只需要不让其参与编译即可，其它源文件不应该进行任何调整;
   - 使用 constructor 的方式将 dissector 注册到解析器链上;
   - dissector 的添加不需要在 engine 中显示进行注册，添加一个 ether dissector 并不需要在 engine 中主动进行 load;
   - dissector 默认仅有 xxx.c 文件，一般不提供 xxx.h 文件;

*** trailer
    - trailer 是什么?
      我们认为 trailer 不只是 ether trailer, 因为有可能 link 层并不是 eth; 例如一些非标准的网络设备或者软件无条件在 packet 末尾追加了
      固定的字节串，比如可能是 raw ip; 将其称作"广义的 trailer";
    - 我们是否要支持"广义的 trailer"? 例如非 ip 协议，packet 的最后有 trailer;
      需要支持，例如在 fuzhou 项目中使用 pprov 时用户固定追加了一些数据在末尾，真正到现场时发现是 raw ip 数据;
    - 如何检测 packet 存在 trailer?
      - 首先 layer 指的是它的所有数据，包括通常的 header 与 payload: eth layer 表示 eth 头与所有它之后的数据，ip layer 表示 ip 头与所有它之后的数据;
      - 一个 layer 表示的是一个 range, 它应该知道自己这一层有多大的 size; 随着解析的一层层递进，当最终停止的时候，框架应该能知道当前消耗到了"哪里";
      - 最后的残留部分，认为是 trailer;
      - 某些 layer 自身有信息可以知道自身有多大的 range, 例如 ip layer; 但其之后的 tcp/udp 没有相关信息表明，我们认为后续协议自动继承上一层的 ending 边界;
      - 所以，我们需要一个 packet 中至少有一层可以告知它的 layer 长度，用来标定其"ending 边界"; 考虑 eth/ipv4/ipv6/tcp/http 的场景，无论是 ipv4 还是 ipv6
        在这里都会宣告它们各自的 layer 长度，但从 ending 来看其实它们是一致的;
      - 在 eth/ipv4/tcp/http/trailer 的场景中，给到 tcp 时的 range 会被"裁剪", tcp dissector 看不到 trailer 部分;
    - 如何计算 trailer 的长度?
      - 我们提供 nxt_engine_layer_set_total_len 接口，知道自身 layer 长度的 dissector 需要积极的进行设置, 这样框架就可以知道最终消耗到了哪里; "残余" 部分
        就是 trailer;
    - engine 通过 nxt_PacketZone 中的 layerBegin_ 与 layerEnd_ 表示当前 layer 的起止范围，setLayerTotalLen 负责更新 pktEnding_, onDissectorDone 负责更新 offset_;
    - engine 在 nxt_Engine::callDissector 与 nxt_Engine::dissectTrailer 中均会产生一个新的 mbuf_view, 这个 mbuf_view 只能看到它应该解析的 layer 的开始与结束;

** engine 工作模式
   - 一个 engine 只能工作在一个 thread 中;
   - packet 驱动，犹如手动榨汁机，engine 运行的驱动力来自"packet 喂入" 的力, 没有新的 packet 到来它就不工作;
     在不需要高性能的应用场景中可以使用这种工作模式，例如将 engineNext 嵌入到一个 python 应用中完成 telnet 口令字提取;
     在实时流量场景下，packet 驱动会拖慢 packet 的接收，packet 被 engineNext 处理之后执行流才会回到收包逻辑;
     另外，这种模式不利于流超时，假如实时包回放，在包全部回放结束之后，engineNext 将没有 packet 输入，这时 engineNext 中残留
     的未超时流将没有机会在被处理(engine 不再运转); 补救方法是引入一个 timer 线程，专门执行 timer 相关处理;
     nxt_engine_run;
   - 自驱动，犹如电动榨汁机，即使没有 packet 喂入它也在运转;
     实现上由 engineNext 库提供 nxt_engine_loop 方法，应用将其运行在独立的线程中，应用中 packet 喂入动作在其它线程中执行，
     两个线程通过 packet-ring 进行数据传递;应用可以自行将 packet 驱动包装到一个线程中实现自驱动，配合 packet-ring 实现；
     timer 线程仍然是必须的，不过多个 engine 自驱动线程可以共用同一个 timer 线程;
     nxt_engine_loop
** 输出 message 的传递
   - eventHandler 回调
     这种方式实现简单，问题是我们不能控制应用提供的 eventHandler 的复杂程度，假如它比较耗时则会拖慢 engine 去处理下一个 packet;
     如果在实时流量并且 packet 驱动场景下，会将'miss' 前移到收包环节，并且这时它还不知道自己丢包了，因为这是最源头；
     我们应该确保收包不能丢;
   - message-queue
     使用 message-queue 给到用户，则 engine 将 message 投递到 queue 则可以立即返回，
     即使用户侧处理 message 太慢导致 queue 满，engine 会将 message 丢弃后继续执行，而不会影响到收包;
     另外，message 的生命周期是由 engine 创建，但由用户侧进行销毁;
     message-queue 方式也可以基于 eventHandler 方式实现，用户侧自己实现队列，eventHandler 中只进行入队操作，
     再配置 message 引用计数功能，将 message 的共享给用户侧执行计数"加一"，而 engine 总可以在 eventHandler 调用之后执行计数"减一"操作;
** engine 与 dissector
   - 既然决定了不同的 engine 中可以有不同的 dissector, 那么 dissector 以及 handoff 关系应该是 engine 中的资源;
   - 只是所有的 engine 中均会的 builtin dissector;
   - 每个 engine 中均有自己的 dissectorKeeper;

** 关于 pmessage 的构成
   - pmessage 由 psession 与 precord 两部分构成;
   - psession 中有关于 session 的必要信息(五元组，统计信息，协议 layer 列表等), 还有 flow, 原始 packet 等;
   - 如果是报文类型的协议如 dns, 则 precord 就表示当前 packet 的各个层级协议元数据;
   - 如果是流式类型的协议如 http, 则 precord 的 app layer 是分片组装后的元数据，而底下各层 layer 为最后一个分片 packet 的各层元数据;
   - 关于会话，如果一个 tcp 连接中进行了 n 个 http 请求-响应，我们应该输出一条 pmessage, 还是 n 条? 还是 2n 条?


* TODO 框架
  - [X] 搭建基本框架，构建 mbuf, engine, message, eventHandler,
  - [X] 完成 packet ether 基本解析, 到 precord;
  - [X] handoff-table
  - [X] dissector 插件可以独立编译为 so, 它需要的依赖均移动到 include/ 目录中;
  - [X] 关于 dissector 加载与 nxt_init 的先后次序问题，plugin 可以在 nxt_init 之后 engine 创建之前加载;
  - [ ] 内置 tcp dissector
  - [ ] current dissector 与 next dissector 之间的数据传递:
    - next dissector 不一定是继续处理 current dissector 的 mbuf, 可能是一个全新的，例如 tcp 重组后的;
    - next dissector 有可能得到了 session, 但 current 还没有创建出 session, 例如 eth/ip/tcp 这些都还没有 session;
    - 通过 engine 进行传递，由 current dissector 进行 set, 框架调用 dissector dissect 之后从 engine 中取出，通过
         dissect 函数的参数将 mbuf, session 传递给 next dissector;
    - 不考虑多个线程中同时操作一个 engine 处理不同的 dissector 的场景，不同线程使用不同的 engine;
    - 需要考虑这时候 mbuf, session 的内存管理，谁创建? 谁释放?
  - [ ] dissector 构建新的 mbuf: tcp dissector 消费的是接收到的 mbuf, 但框架需要支持一种机制，
        由 upper dissector 生成 lower dissector 需要的 mbuf, 这个 mbuf 是由 commit ring buffer 构造出来的;
  - [ ] 基本流程: 会话管理 -> 前k包缓存 -> 识别 -> 重组 -> 解析, 至少积累 k 包才进行识别;
  - [ ] 关于 engine 中的 register:
        - register 系列变量存储当前 packet 中解析出的相关信息
        - 用于在各级 dissector 之间进行传递数据;
        - 不存储在 session 中是因为
        -  1) 无法存入到 session 中，例如解析 ip 层时还没有完整五元组;
        -  2) 这里更多表示 packet 信息，每一个 packet 都会变化;
        - 是否通过 dissect 函数的参数进行传递会更方便呢？它随着栈进行创建与销毁？并且很明确它不是持久的;
          这意味着需要暴露一个结构体给 dissectors, 不利于维护; 除非是一个不透明指针，通过接口进行 set/get 操作;
  - -------------------------------------------------------------------------------------------------
  - [ ] plugin 任何时候加载? 那么如何'暂停 engine'? 不，只支持在启动时加载，具体地，不支持在进行 nxt_engine_run 之后加载插件;
  - [ ] 需要有插件管理器，so 在程序退出时时需要执行 dlclose 进行卸载的，否则会有内存泄漏;
  - [ ] 增加对 NXT_REGISTER_DISSECTOR 时 handoff_field 字段的校验，在调用 init_fun 时其中必须注册有 handoff_field 字段;
  - [ ] 如果 dissector 是全局的，则一旦加载了插件 foo, 假如 tcp port 9000 handoff 到 foo, 则这个 handoff 关系对所有 engine 均生效，
        我们不能在不同的 dissector 之间进行差异化处理; 这个限制影响比较大，不能接受, 今后的 engine 应该可以做到按业务进行隔离;
        某个 engine 加载某些 dissector 插件, 接入相关流量; 不至于因为一个 engine 中的 dissector 故障(如死循环) 所有 engine 均不能工作;
  - [ ] 协议识别-基于端口
  - [ ] 协议识别-dissector 可提供识别函数，用于识别非标端口协议;
  - [ ] handoff_field 支持多个字段，形成一个数组;
  - [ ] 会话查找使用对称 hash 算法
  - [ ] 引入标准 encap type;
  - [ ] 引入标准 ether type enum;
  - [ ] 引入标准 ip proto enum;
  - [ ] 增加接口获取 last_error;

* TODO 会话管理
  - [ ] 会话管理-设计会话 item, 会话表，会话 key;
  - [ ] 会话管理-基本重组, 重组提交给 dissector 消费之后立即释放;
  - [ ] 会话管理-与基于 tcp 的 dissector 结合，例如 rtsp
  - [ ] 会话管理-packet 缓存
  - [ ] 完善事件机制，支持 PE_MESSAGE_ARRIVE, PE_PACKET_ARRIVE
  - [ ] 内置 udp dissector
  - [ ] 支持前 n 包重组，n 包过后，session 进入到 EMIT 状态(直接触发 PE_PACKET_ARRIVE)，例如 ssl;
  - [ ] 支持 PE_PACKET_ARRIVE 事件，实现 ssl 某 host 会话 packet 全部 dump;
  - [ ] 加入包缓存机制，默认缓存前 k 包(暂不重组), 但第一个 PE_MESSAGE_ARRIVE 到达后必须主动要求继续缓存，否则不再缓存;
        可用于实现 sdt 筛选规则命中后输出会话流;
        准确地讲，包缓存在状态 1 时缓存‘最近 k 包’， 当主动要求持续缓存之后，将增加队列一直缓存，直到应用层消费掉 packet;
        包缓存与 tcp 流重组不相关，各自独立工作；例如 udp 会话也可以进行包缓存;
  - [ ] 在 dissector 注册时其就可以声明 userdata 大小，engine 在创建 session 时为其准备好，但是这意味着需要知道这个 session 的应用层协议;
        一个会话只能有一个应用层协议，不能有多个，在 sip/sdp 场景中，这个应用层协议就是 sip;
  - [ ] 还是需要有识别函数，在识别到一个会话是某应用层协议时，为其创建 userdata;
  - [ ] 会话表使用对称 hash 算法实现，使用一个定制的 hash 表;

* TODO tcp 流重组
  - [ ] tcp dissector 负责执行流重组，当发现数据有序增长时通知框架可以调用 next_dissector, next_dissector 进行解析尝试，有可能还不足以进行解析;
  - [ ] reassemble 模块设计 reassembleList, c2sBuffer, s2cBuffer;
  - [ ] 流重组需要有 mbuf reassemble list, 还需要有 commit ring buffer(例如 64k), 用于有序递交数据给 next_dissector;
  - [ ] dissector 注册时可以指明需要的 tcp 重组包数，是否重组是以协议为单位进行区分处理的，协议注册时支持：
        -1: 无限重组(例如 ssl),
        0: 不需要重组(某无法解析的协议，例如通过端口识别后仅做统计),
        n: 重组前 n 包(例如 ssl, wxf);
  - [ ] unknown 协议不为其进行重组，unknown 不同于 not_identify_yet;
  - [ ] nxt_Flow 中会原样记录 enqueue 进来的 mbuf, 称作 timeline, 保留原始 eth, ip, tcp 等，并且停留到达顺序，以备进行 dump 或者 forward;
  - [ ] timeline 保留多长时间的数据呢? 默认缓存前 n 包，只有应用层主动要求还需要继续缓存，才缓存更多数据;
  - [ ] nxt_Flow 重组时提供的是 enqueue 与 dequeue 的抽象，确保 dequeue 时是有序的，enqueue 进来的 mbuf 在提交时不一定是保持原样的;
        比如发生了 tcp 重传，ABCD, 其中 A 与 B 可能有重叠的部分, 我们会存储为 AbCD 这样;
        因为可能会对 enqueue 进来的 mbuf 进行修改，那么我们不能直接将 enqueue 进来的 mbuf 保存到 list 中，
        应该先将 mbuf 存储到 timeline 中，list 中的 mbuf 引用 timeline 中的 mbuf 的部分数据;
        确保 list 不拥有任何的 mbuf;
  - [ ] 重组是否需要内存池? 内存池可能是一个需要由外部注入的功能，next 支持配置内存池，但它不是必须的;
  - [ ] 因为内存的代价，timeline 不是持续工作的，reassemble 也不是持续工作的;
  - [ ] mbuf 还是需要实现引用计数，不论是 timeline 还是 reassemble 共同管理 mbuf;
  - [ ] nxt_flow 与 mbuf 管理，flow 作为 mbuf 的拥有者负责 mbuf 的销毁，管理在 timeline 中，当 timeline 超过上限时会被自动销毁;
        暂时让 reassemble 问题拷贝一份来自 timeline 的 mbuf 数据，后面会优化为引用;
        reassemble 的 mbuf 在 dequeue 之后被销毁;
  - [X] 不再提供 nxt_session_dequeue_packet 接口，mbuf enqueue(将副本入队) 之后，由 nxt_session_stream_read 进行读取(copy 到目标 buf);

* session 与 mbuf 以及 precord/pevent
  - 一个 precord 代表一条消息，何时解析出一条新消息只有应用层知道
  - session 中根据不同方向应该有 precord 模板，保留 eth/ip/tcp 信息
  - 对于 n 包 tcp 构成的一条 http 消息，只在第 n 包产生 http precord, 这其中的 eth/ip/tcp 反映的是第 n 个 tcp 包的信息;
    问题是如果有多层嵌套怎么办？vlan, esp 等？可能需要区分 '承载 precord' 与 'app precord'， 或者叫 packet precord 与 message precord;
  - dissect 接口上不应该有 precord, precord 应该由应用层通过 session/mbuf 进行创建，会填充相关 eth/ip/tcp 信息;
  - packet precord 由框架自动创建; message precord 需要由 dissector 主动创建并存储到 session 中；
  - event 目前是在 packet 解析返回之前就需要完成回调，意味着如果 eventHandler 处理不及时会阻塞下一包的处理;
    再比如，如果在 eventHandler 中执行了磁盘 io 操作，将会大大拖慢前面的包处理速率;我们可以要求用户的 eventHandler 中
    只能执行快速的操作，但难免会被误用；
  - event 需要进行区分, 有 packet-message 与 session-message;
  - event 时报告的 pmessage 中还需要增加 session 提供给使用方


* tcp 流重组
** 需求
   - 支持乱序重排;
   - 支持 segment 之间的 overlap 处理;
   - 支持重组过程的事件通知，供外界窥探重组过程，例如新有序提交了某个 segment(相关信息包括 seq, len, payload 等);
     - 事件足够支撑将整个 tcp 会话 dump 为 pcap, 包括 sync, pure-ack 帧;
   - 支持丢包报告，应用层协议可以知道在什么位置丢失了多少字节，可以自己决定是否填充；框架不内置这样的功能;
   - 支持双向交织，需要设计一套模型如何让应用层使用相关信息;需要支持的场景比如在 telnet 中 S2C 遇到了'login' 这时需要知道针对它的 C2S '响应';
** 设计
*** 数据结构
    - TcpFlow: 表示一条 tcp stream, 其中有 state, reassQueue, commitQueue, expectSeq;
    - TcpSegment: 一个 segment 中有 tcp 分节必要的 seq, len, mbuf, 为 queue 准备的 next 与 prev, 以及相关'裁剪' 信息, tcpHeadOffset 等;
    - TcpSegmentQueue: segment 队列，用来实现 commitQueue; commitQueue 中只保存排好序的 segment;
    - TcpReassQueue: 重组队列，处理排序，重叠处理;
*** 流程: TcpFlow 的 enqueue 函数
    - input: 新到达一个 mbuf, 包装为 segment, 比对 seq 与 TcpFlow 中的 expectSeq, 如果相等，提交到 commitQueue; 通知'canRead';
    - reass: 将 segment 有序插入 TcpReassQueue; 通过 seq 跳过所有比我小的，这时位于 p 与 n 之间，分别与 p 和 n 以及 n 的后续比较是否有重叠, 进行裁剪;
    - commit: 检测 TcpReassQueue 是否有可以与 expectSeq 连续的，将所有连续 segment(直到遇到有缝隙的) 入队到 commitQueue;
    - notify: enqueue 报告是否可读;
    - read: 从 commitQueue 中 read; commitQueue 是由 mbuf 构成的链表，并不是一个巨大的连续的 buffer;
** 问题
   - 需要会话支持，需要实现超时；否则将可能有残留数据一直无法提交;
   - 何时该通知应用层协议解析器读取数据?
   - 如果 read(1024) 但无法满足，我们应该尽可能给还是等缓存足够一次性提供?
   - 发生丢包，如何提交数据? 如何判定丢包? 万一认为丢失的包后续又来了呢?
   - 遇到 fin 怎么办? 它有可能是乱序到达的; 遇到 fin 之后我们可以在将来知道流结束了，需要及时拆除连接;
   - 没有遇到 fin 怎么办? 应该超时之后进行会话的销毁;
   - 超时时间不宜过长, 5-10s 就够了，超时拆除;
   - 如何应对一条流反复重放?

** 设计
* engine 间通信
  - mbuf 有 type 字段，其可以用来区分 ctrl-message 与 packet-message;

* dissector 接口
  - [ ] 提供 nxt_stream_ 系列接口，有 read, get_len, peek 等功能，同时通过 nxt_segment_status 反馈 seq, 丢包等信息;
  - [ ] 提供 nxt_stream_raw_ 系列接口, 有 first, next 用于遍历 raw mbuf, 会看到被'重整' 过的底层'rsm-done'  mbuf list;
  - [ ] session 应该提供 nxt_session_malloc 等接口用于支持应用层管理 session 需要的内存，例如缓存的应用层消息等;
  - [ ] 提供 nxt_precord_new 接口，用于产生新的 record message, 通过 nxt_precord_push 接口提交给 engine?
  - [X] int dissect(nxt_engine_t *engine _U_, nxt_session_t *session, nxt_mbuf_t *mbuf _U_) 接口中，
        mbuf 表示当前新到达的 packet, 它并不是有序的; 获得有序 packet bytes 需要通过 nxt_session_stream_read 接口;

* engine 中各种数据的生命周期
  - layer_zone: 在各个 layer 之间传递数据，不拥有数据;
  - packet_zone: 仅在一个 packet 的处理期间有效，在 packet_reset 时进行清理;
  - session_zone: 仅在一个 session 的处理期间有效，在 session_reset 时进行清理;
  - engine_zone: 在整个 engine 中都有效，engine 结束时清理;

* utils
  - [ ] 需要有 nxt_mbuf_hex_dump(nxt_mbuf_t *mbuf);

* think-twice
  - [ ] 2023.10.13: nxt_init 与 nxt_fini 是否必须，能否全部为基于 engine 进行操作，除 nxt_mbuf_xx 等数据结构相关 api 操作;
  - [ ] 2023.10.13: plugin 初始化函数的执行模式，constructor 与 called-by-loader;
  - [ ] 2023.10.13: plugin 是否需要版本、协议名、协议工作层级等信息?
  - [ ] 2023.10.13: 关于 dissector 倒序注册的影响，可能加载次序是 ip, tcp, http, 但会先执行 http, 并且先解析 http 的 resolve, 不妥;
  - [ ] 2023.10.13: nxt_plugin_dissector_load 接口是否需要带上 engine 参数？插件加载是否应该是每个 engine 各自加载，需要与 nxt_init的移除一同考虑;
  - [ ] 2023.10.13: 关于 nxt_mbuf_t 的创建与销毁，目前计划由 user 创建，由 engineNext 进行销毁，
    最终其实是作为 pmessage 的一部分，在回调事件处理器之后被销毁；除非有引用计数机制实现后，可以被 user 延长生命周期;
  - [ ] 2023.10.13: 非标端口等问题;
  - [ ] 2023.10.13: 如何发现插件 dissect 中发生的死循环?
  - [ ] 2023.11.28: nxt_session_stream_read 在不中以满足需要的数据时是否部分返回？目前不足时不提供任何数据;

* 需要注意的技术问题
  - 2023.11.24: 关于含有 'flexible array member' struct 内存重叠问题，如果该 struct 的一个实例没有为该 member 赋值这部分并不存在，
    而如果去访问它时可能访问的是其它变量的内存; 参见 nxt_dissector_eth.c 中的 NXT_REGISTER_DISSECTOR(eth) 部分，必须为 mount_at 赋值;
