#+TITLE: unit_test
#+AUTHOR: zhengsw
#+DATE: 2024.11.07

* 概述
  单元测试对于软件开发的质量控制至关重要，我们不能仅依靠整个软件版本提交给测试部门后进行的集成测试。单元测试将带来以下收益：
  - 单元测试可以给开发人员信心，各个模块通过了它们各自的单元测试，最终通过集成测试的可能性更大;
  - 让我们可以放手重构，单元测试中定义了模块必须要兑现的承诺，当我们进行模块实现的调整时，单元测试是一个有力的检测标准;
  - 单元测试更易于构建输入，精准、直接地用特定场景测试相关模块，例如相比集成测试整体程序需要 live 流量、pcap 数据等，模块的单元测试可以直接在代码中
    1) 以文本形式直接描述 http 协议，从而验证 http 协议解析;
    2) 直接构建异常场景，例如'源目端口相同', 'ip 报文实际长度与相关指示不符'等;

* 设计原则
  - 单元测试可以通过 yaEngineNext 的公开接口进行黑盒测试, 但这些更偏向整体功能，主要由 test/yaLiteDpi 程序负责;
  - 单元测试的主要目标是 yaEngineNext 的内部模块如 mbuf, parser, timer, recognizer;
  - 所有的提交需要确保单元测试均是通过的，必要是可以 disable 相关测试;
  - src/ 目录下的模块 foo.[h/cpp] 在 test/unit/ 目录下有一个对应的 foo_test.cpp 完成单元测试;

* 项目组织
  - 单元测试与业务功能代码分离，单元测试代码位于 test/unit/，业务代码位于 src/
  - 单元测试最终由可执行程序 bin/yaNxtUnitTest 进行测试; 它需要可以看到 src/ 下的模块定义，
    所以 yaEngineNext 将 src/ 作为了 INTERFACE include_directory(见 CMakeLists.txt 中 'target_include_directories' 部分);
