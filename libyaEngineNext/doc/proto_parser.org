#+TITLE: proto_parser
#+AUTHOR: zhengsw
#+DATE: 2024.07.12

* ragel 是什么?

* 设计原则
 - 易于使用、修改
   - 以 ragel 格式描述的协议语法文件 foo.rl 应该易于生成;
   - 添加新字段的流程应该尽可能简单;
   - 是否易于移植到其它项目中使用 ya_parser_rtsp.rl?
   - ragle 实现的 parser 位于一个独立的文件中, 例如 ya_parser_rtsp.rl,
     nxt_dissector_rtsp.c 与 yafsm_parser_rtsp.rl 分离，yafsm_parser_rtsp.rl 仅提供 parse 函数，
     - 可维护性
     便于测试、调试，以及其它项目中的复用; 如果我们仅提供一个 nxt_dissector_rtsp.rl 则项目中的任何修改均
     需要使用 ragel 生成生成 nxt_dissector_rtsp.c 文件, 并且调试时不便于对应到源码，以及在编辑 .rl 文件时
     主流的 ide 与编辑器均不支持跳转;所以需要尽量限制 .rl 文件中应该含有尽量少的的 .c 代码;
 - 易于调试
   - ya_parser_rtsp.rl 应该只提供一个 nxt_parse_foo 函数;
 - 异常情形：
   - mbuf 长度不足以解析时，需要支持"流式解析", 当下一次给出完整 mbuf 时不用从头再解析一次;
   - 当遇到不符合语法的场景时需要可以报告具体位置，直观地说明是哪里不符合;
 - 支持流式解析
   - 当给出的 (buff, len) 不足以解析出一条消息时，应该可以记住状态，在同一条流上的后续内容到来
     时可以恢复继续解析;

* ragel 使用规范
  - 使用 ragel 的组织方式：
    - grammar_rtsp.rl
    - parser_rtsp.rl 实现了函数 yafsm_parse_rtsp; include 了 rtsp_grammar.rl
    - nxt_dissector_rtsp.c 使用了 ragel_rtsp_parse 函数来实现 rtsp 协议的解析;
  - 命名
    - machine 命名：由小写字母, 下划线组成;
    - action 命名;
  - 缩进, 每行缩进4个空格;
  - 关于生成 .c 文件中的行号
  - 关于状态数的限制
  - ragel 中的 include 依赖, 无法触发依赖项的自动构建;
  - 如何准确提取: 一个 symbol 是由它的前后 symbol 共同标识的，而不单单是它的名字，例如 rtsp 中的 Transport 中的 ssrc;
  - 如果要提取字段 foo, 则应该在 foo 的参与式中进行提取，而不应该在 foo 的定义式中提取；例如 sip 协议中的 method 定义:
    Method = (INVITEm | ACKm | OPTIONSm | BYEm | CANCELm | REGISTERm | extension_method);
    Request_Line = Method SP Request_URI SP SIP_Version CRLF;
    CSeq = "CSeq" HCOLON DIGIT{1,} LWS Method;
    应该提取 "Request_Line 中的 Method", 如果直接在 Method 上提取 Method 和 CSeq 上的 Method 都会被提取到;

* ragel(.rl) 文件规范

* ragel 的问题：
  - 随着需要提取的字段增加，action 增加，状态也会增加(不为某些状态迁移添加动作时可能有状态合并); 生成的 .c 文件也会越来越大;
  - 当前无法支持嵌套提取：例如
    Via = ("Via" | "v") HCOLON (via_parm >mark %via_p (COMMA via_parm)*) >mark %via;

* ragel 注意事项
  - from, to 等不能用做 action 名字，需要调整为 _from, _to;

* ragel parser 与 ringbuf 的配合使用

* 流式解析-2024.08.04
  - tcp 是一个没有边界的流式协议，基于 tcp 的应用层一般有设计“消息帧”结构，即 "message framming";
    parser 一般设计为解析一个一个的 message, tcp 仅仅负责重组它递交一个一个的 segment, segment 与 message 并不对应;
    这就需要在 tcp stream read 与 parser 之间有一个“消息接收缓冲区”, 都先存储下来，之后由 parser 进行 "decode";
  - 这个缓冲区也是有限的，例如使用 http 下载一个 10MB 的文件，可能仅仅是一个 http 的 GET 响应消息，但我们不可能为所有
    的会话都准备如此大的缓冲区; 所以 parser 需要支持流式解析;
  - 那么，我们使用一个链表将 m 个 buffer(大小为 n) 连接起来是否可行？不妥，buffer 与 buffer 之间并不连续，例如一个 http.user_agent 字段
    可能被“截断”为两部分;
  - 直接使用一个大小为 m*n 的 buffer? 不妥，例如 buffer 大小为 300，有 msg1(50), msg2(80), msg3(200), 假定 tcp 到来 4 个segment 分别是 100，
    100，100，30; 当第二个 100 字节到来后 msg1 与 msg2 解析出可以将其空间标记为空闲，但最后的 30 字节尾部已经没有空间可以存储，而首部有 100
    字节的空闲空间；如果分开存储，还是会有截断问题;
  - 使用 ringbuf, 可以解决上述问题，但是目前这种方式配合 parser 依然要求 ringbuf 中可以存储下一个完整的 message;对于 msg3 虽然调用了三次解析，
    但是在 msg3 整个解析完成之前它占据的空间即使已经处理的部分也不能继续用于存储新的接收;
  - 需要准备一个测试用例: ringbuf 长为 200，msg1(120), msg2(50 + 70), 这将使得 msg2 的 70 到来时发生 memmove, 我们需要在这种情况下提取出正确的
    70 后半段;
  - parser 暂时先使用 ringbuf 实现，后续实现更实用的“流式解析”，需要提供以下 append_out 机制:
    - append_out: 为消息的某字段追加内容; 在一次 partial 解析中，parser 需要记住当前有某字段并未解析完成，但需要尽可能吐出来;
      等到下一段到来继续解析时，自动将开头进行 mark, 在结束时继续使用 append 方式进行提交;;
    - 这要求 precord 的 field value(string) 是可以 append 的;
    - 但是这样也有问题，在 mark 时可能还不能确定需要 append 给哪个字段，例如："user", 可能是 "username" 也可能是 "userpasswd";

* 流式解析方案
  - 方案一: 检测到 token 截断时执行 rollback, 吐出 partial token;
    - token 的标记, (p, len);
    - memmove 场景: nxt_parser_rtsp_parse 如果遇到截断的 token 并不消耗它；这样可以尽量、及早释放 ringbuf;
      但这样实现较复杂，如果这时候发生了 memmove, mark 的那一部分该如何保证其依然有效？没有关系，我们会进行“回退”，
      等到下次再进行 parse 时应该从 token 的开始处重新进行 parse, 那么这时候 mark 会重新被执行;
    - parse 模式: ringbuf_push_back, parse, ringbuf_pop_front, ring_push_back, parse, done;
    - 限制: ringbuf 需要可以完整容纳下任何一个 token;
  - 方案二: 在 ringbuf 中存储整个 message, 直到解析 complete, 才将 message pop_front;
    - token 的标记, (offset, len), 通过 (buf[offset], len) 得到 token; 所以 ringbuf 中需要存储从开始到结束的所有 data;
    - memmove 场景: 在截断时记录了 offset, 发生 memmove 后下次进行 parse 传入 new_buffax 起始位置，next_offset(计算新的解析位置),
      当 token 提取完成时, 使用 new_buff[offset] 依然可以得到 token 的起始位置;
    - parse 模式: ringbuf_push_back, parse, ringbuf_push_back, parse, done, ringbuf_pop_front;
    - 限制: ringbuf 必须可以完整容纳下任何一个 message;
    - 接口使用其实并不方便，parse 上需要成为: parse(parser, buff, len, offset, userdata);

* 当前方案:
  - 要求: 限制比较大;
    - 要么 nxt_parser_rtsp_parse(parser, buff, len, userdata) buff 足够大，其中有完整的消息;
    - 流式解析，分段给入，
      nxt_parser_rtsp_parse(parser, buff, len, userdata);
      nxt_parser_rtsp_parse(parser, buff2, len2, userdata);
      如果 buff 解析为 partial, 则 buff2 必须是 buff 后面的连续地址; 因为 buff 中可能记录了 token 的起始位置;

* http 解析中的启示:2025.01.14
** 目前的模型：
   - 框架中 tcp 进行重组，使用 ringbuf(大小为 4000字节) 从重组队列中有序读取数据;
   - 将 ringbuf 作为输入给 http parser 进行解析;
   - http parser 通过 nxt_parser_get_status 告知 NXT_PSTATUS_COMPLETE 表示解析得到一条'完整的消息';
   - 问题是, http parser 只能知道目前给它的部分满足一个'完整消息'的语法结构，但其实可能并不完整，例如:
     "POST ... body" + "more body" 场景中，在第一次 ringbuf 读取时已经符合'完整消息'结构，但其实消息并没有结束;
** 消息完整不应该由 parser 来决定:
   - parser 就是一台机器，能不能继续工作，有没有进一步的输出，取决于是否还有输入;
** http 解析要点:
   - http 解析流程有以下状态：ST_HEADER, ST_CONTENT_LENGTH_BODY, ST_CHUNKED_BODY;
   - body 的识别，判断是否有 body,
     1.有 "Content-Length" 字段并且大于0;
     2.有 "Transfer-Encoding: chunked";
     3.http 1.0 中没有 "Content-Length" 并且有 body 时，body 直接发送，直到 socket 关闭; 每个 tcp 连接中只有一个 http 消息;
   - body 的提取：
     1.直接提取;
     2.multipart 类型,例如: Content-Type: multipart/form-data; 需要进行切分;
     3.压缩类型，例如: Content-Encoding: gzip;

** http 解析TODO:
   - [ ] 简化 http ragel 文件, 例如 uri, path 可以简化，并不关心其中细节;
   - [ ] head 大小写问题;
   - [ ] 同一 head 出现多次问题，例如 Set-Cookie
   - [ ] multipart 问题;
   - [ ] upgrade 为 websocket 问题;

* todo:
  - 关于文本类网络协议的解析，需要使用 ragel 的模块化方式即使用多个 parser 进行 fcall, fret 的方式进行调用；
    因为例如像 sip 这样的协议实在太复杂了，表示为一整个“正则表达式”生成 dfa 会发生 dfa 状态爆炸，甚至无法编译结束;

* 当前存在的问题-2024.11.11
  - [ ] ringbuf no space 问题，关于‘单个字段’(例如 http body)超出了 ringbuf size 的问题;
  - [ ] 没有与 ftype 配置支持字段的'增量' append, 因为一些字段可能在两字 mbuf 中间被拆分开来;
  - [ ] action OUT 仅仅支持 YA_FT_STRING 类型的 fvalue;
  - [ ] ragel parse 代码调试不便;
  - [ ] 每个 nxt_parser_xxx.rl 文件中的 nxt_parser_xxx_init, nxt_parser_xxx_parse 几乎是相同的模板代码;
  - [ ] http, rtsp dissector 中存在大量重复的模板代码;
  - [ ] http, rtsp dissector 中都需要 userdata, 并且它们也都是相同的模板代码;

