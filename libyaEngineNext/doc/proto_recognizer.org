#+TITLE: proto_recognizer
#+AUTHOR: zhengsw
#+DATE: 2024.11.06

* 概述
  软件协议被组织为一种层级结构，在典型的 eth/ip/tcp/http 场景中我们需要一层一层向下解析;通常上层协议会有相关标识来说明下层协议的身份，例如 eth.type, ip.proto;
  在 tcp 与 udp 场景中可以根据 server port来识别下层协议，但这样做并不可靠，存在以下例外：
    - 知名协议使用非标端口：http 协议可能被使用在非 80 端口上，例如 8000, 9001 等;
    - 知名端口被其它协议占用：tcp 443 端口上的不一定是 ssl, 可能是 wxf;
  一些原因可能如下：
    - http 使用最广泛，为了在一台服务器上部署多个 web 服务不得不使用非 80 端口;
    - smb 协议因为从前出现过安全漏洞常用端口通常被封禁，可能被会配置为不同端口; rdp 协议也可能有类似情况;
    - smtp 端口上可能实际运行的是 pop 协议;
    - 网络中的防火墙可能仅开放了 80, 443 等常用端口，一些服务可能为了便利而'借道';
  以上情况存在，但主要是一些‘常用协议’容易出现这类现象：但也存在一些协议‘一成不变’使用自己的端口，例如 udp 53端口几乎总是 dns 协议，gtp-u 总是运行在 udp 2152 端口上;
  我们不能仅使用端口来识别协议，也不能仅使用‘特征字’来识别(有些协议无法使用特征字识别例如 dns 与 gtp-u, rtp 等); 需要对不同的协议有不同的手段；
  eth 下层协议的识别，ip 下层协议的识别均可以通过'next' 进行标识(另见 handoff 设计)，这里主要关于 udp/tcp 下层的协议识别问题;

* 问题
  - 对于 rtp, ftp 这些动态场景的识别需要有一定的支持，协议识别模块应该可以在运行时进行更新，添加一些临时'表项';

* 设计目标
  - 同时支持使用端口与 payload 特征字来识别协议; 二者可以同时限定，其中之一也可以是可选的，使用 NXT_ANY_PORT, NXT_ANY_PAYLOAD 表达;
  - 协议识别算法必须是快速的，不能随着协议数量的增加识别性能下降;
  - payload 特征字使用正则描述，是 search 语义而不是 match 语义，例如 "^GET" 表达'tcp payload 以"GET" 开头';

* 设计
  - 整体采用两级 payloadPatternTable, 分别用于'带端口 payload 特征' 与 '不带端口 payload 特征';
  - 第一级表由 (port, ((pattern1, proto1), (pattern2, proto2), ...), 或者 (port, proto) 条目构成;
  - 第二级表由 (pattern1, proto1) 条目构成;
  - 协议识别时，根据当前会话(注意不是 packet, 一个会话仅进行一次协议识别)的 server port 到一级表中查询，
    如果没有 pattern 则直接返回相应条目的 proto; 如果有 pattern 则进行模式匹配，命中则返回相应 proto;
  - 如果在一级表中对应的 port 条目不存在，或者模式匹配没有成功，则到二级表中进行模式匹配，返回相应 proto;

* todo
  - nxt_, 由 (parentProto, port, payloadPattern, proto) 构成一个注册表项;
  - nxt_ProtoRecognizer
