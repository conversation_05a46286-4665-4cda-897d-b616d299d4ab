#include "yaEngineNext/nxt_parser_core.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

nxt_parser_t* nxt_parser_create_wa(ya_allocator_t *alloc, nxt_parser_init_fun initFunc)
{
    nxt_parser_t* parser = (nxt_parser_t *)ya_allocator_alloc(alloc, sizeof (nxt_parser_t));
    if (initFunc)
    {
        parser->initFun = initFunc;
    }

    nxt_parser_reset(parser);
    nxt_parser_clear_rollback_point(parser);
    return parser;
}

int nxt_parser_destroy_wa(ya_allocator_t *alloc, nxt_parser_t *parser)
{
    ya_allocator_free(alloc, parser);
    return 0;
}

nxt_parser_t* nxt_parser_create(nxt_parser_init_fun initFunc)
{
    return nxt_parser_create_wa(ya_allocator_get_default(), initFunc);
}

int nxt_parser_destroy(nxt_parser_t *parser)
{
    return nxt_parser_destroy_wa(ya_allocator_get_default(), parser);
}

int nxt_parser_reset(nxt_parser_t *parser)
{
    parser->ragelCS    = 0;
    parser->ragelPoint = 0;
    parser->status     = NXT_PSTATUS_READY;

    // stack
    parser->top        = 0;
    memset(&parser->s, 0, sizeof parser->s);

    // kv
    parser->k.str = parser->v.str = NULL;
    parser->k.len = parser->v.len = 0;

    parser->initFun(parser);
    return 0;
}

nxt_pstatus_enum nxt_parser_get_status(nxt_parser_t *parser)
{
    return parser->status;
}

int nxt_parser_dump_error(nxt_parser_t *parser, uint8_t *buff)
{
    printf("parse error on:\n");
    printf("%.*s", (int)(parser->ragelPoint - buff), buff);
    printf("<<%c>>", *parser->ragelPoint);
    printf("%s", parser->ragelPoint + 1);

    return 0;
}
