#include "session.h"
#include "mbuf.h"
#include "dissector.h"
#include "engine.h"
#include "yaEngineNext/nxt_util.h"
#include "util/timer.h"
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/allocator.hpp>
#include <string.h>

#ifndef NDEBUG
#define NXT_TIMER_SESSION_EXPIRE 60000 // 会话超时时间，DEBUG 版本 60s
#else
#define NXT_TIMER_SESSION_EXPIRE 300   // 会话超时时间，RELEASE 版本 300ms
#endif

#define NXT_TIMER_SESSION_AGING (1000 * 60 * 5) // 5分钟

static
void onTimer_destroySession(nxt_timer_t* timer _U_, void *session)
{
    nxt_Session *s = (nxt_Session*)session;

    s->getSessionKeeper()->destroySession(s);
}

static
void onTimer_agingSession(nxt_timer_t* timer _U_, void *session)
{
    nxt_Session *s = (nxt_Session*)session;

    s->getSessionKeeper()->destroySession(s);
}

nxt_Session::nxt_Session(nxt_SessionKeeper *keeper, uint64_t index, nxt_tuple_5_ipv4_t *t5)
    : sessionState_(NXT_SESSION_ST_NOT_RECOGNIZED_YET)
    , keeper_(keeper)
    , proto_(NULL)
    , index_(index)
    , t5Ipv4_(*t5)
#ifdef NXT_SESSION_USE_ARENA_ALLOC
    , alloc_(256, keeper_->getAllocator())
#endif
    , flowC2S_(*t5, this)
    , flowS2C_(nxt_tuple_5_ipv4_reverse(t5), this)
    , lifetime_(alloc_.get())
{
#ifndef NDEBUG
    char t5Buff[70];
    int indexPos = snprintf(t5Buff, sizeof t5Buff, "session%07lu-", index_);
    nxt_util_t5ipv4_to_str(&t5Ipv4_, &t5Buff[indexPos], sizeof(t5Buff) - indexPos);
    strRepr_ += t5Buff;
#endif

    // 创建 session userdata
    this->setupUserdata(proto_);

    createTime_ = time(NULL);

    // 启动会话超时定时器
    timerDestroy_ = nxt_timer_create(alloc_.get(), keeper_->getTimerScheduler(), onTimer_destroySession, this);
    nxt_timer_start(keeper_->getTimerScheduler(), timerDestroy_, NXT_TIMER_SESSION_EXPIRE, 1);

    // 启动 aging 定时器，统计超长的会话
    timerAging_ = nxt_timer_create(alloc_.get(), keeper_->getTimerScheduler(), onTimer_agingSession, this);
    nxt_timer_start(keeper_->getTimerScheduler(), timerAging_, NXT_TIMER_SESSION_AGING, -1);  // 无限重复;
}

nxt_Session::~nxt_Session()
{
    auto timerSche = keeper_->getTimerScheduler();
    nxt_timer_stop(timerSche, timerDestroy_);
    nxt_timer_stop(timerSche, timerAging_);

    nxt_timer_destroy(alloc_.get(), timerSche, timerDestroy_);
    nxt_timer_destroy(alloc_.get(), timerSche, timerAging_);
}

void nxt_Session::setupUserdata(nxt_dissector_t *proto)
{
    if (proto && proto->getUserdataSize() > 0)
    {
        nxt_session_userdata_t *userdata = proto->getUserdata();
        userdataInitFun_                 = userdata->initFun;
        userdataFinishFun_               = userdata->finishFun;
        this->allocUserdata(proto->getUserdataSize());
    }
}

void nxt_Session::onProtoRecognized(nxt_dissector_t *proto)
{
    sessionState_ = NXT_SESSION_ST_OK;
    proto_        = proto;
    this->setupUserdata(proto);
}

nxt_Engine* nxt_Session::getEngine()
{
    return keeper_->getEngine();
}

void call_userdata_finish(nxt_Session *session)
{
     session->userdataFinishFun_(session, session->userdata_);
}

void* nxt_Session::allocUserdata(uint32_t sessionZoneSize)
{
    // TODO: 需要检测内存分配失败
    userdata_ = this->alloc(sessionZoneSize);                            // 被 lifetime_ 管理, 执行内存回收;
    memset(userdata_, 0, sessionZoneSize);                               // WARNING: 总是将 userdata 进行初始化; 简化 client 逻辑;

    // 执行初始化操作
    if (userdataInitFun_)
    {
        this->userdataInitFun_(this, userdata_);
    }

    // 创建 defer 用于调用 userdata finish 函数
    if (userdataFinishFun_)
    {
        this->defer((nxt_defer_callback_t)call_userdata_finish, this);  // 被 lifetime 管理, 执行 finish;
    }

    return userdata_;
}

void* nxt_Session::rellocUserdata(uint32_t newSessionZoneSize)
{
    // TODO: 需要检测内存分配失败
    userdata_ = realloc(userdata_, newSessionZoneSize);
    return userdata_;
}

void* nxt_Session::getUserdata()
{
    return userdata_;
}

void* nxt_Session::alloc(size_t size)
{
    return lifetime_.alloc(size);
}

void  nxt_Session::defer(nxt_defer_callback_t callback, void *userdata)
{
    lifetime_.defer(callback, userdata);
}

void nxt_Session::updateSessoinLiveDuration()
{
    this->liveDuration_ = time(NULL) - this->createTime_;
}

void nxt_Session::showStats()
{
    printf("%s live %lus c2s queue:%d,%d, s2c queue:%d,%d\n",
           this->getSessionRepr(), this->liveDuration_,
           flowC2S_.getCommitQueue()->getQueueLength(), flowC2S_.getReassQueue()->getQueueLength(),
           flowS2C_.getCommitQueue()->getQueueLength(), flowS2C_.getReassQueue()->getQueueLength());
}

int nxt_Session::getAvailBytesLen(nxt_direction_enum direction)
{
    if (NXT_DIR_C2S == direction)
    {
        return flowC2S_.getAvailBytesLen();
    }
    else if (NXT_DIR_S2C == direction)
    {
        return flowS2C_.getAvailBytesLen();
    }
    else
    {
        // TODO: 错误的 direction
        return 0;
    }
}

int nxt_Session::processPacket(nxt_engine_t *engine, nxt_direction_enum direction, nxt_mbuf_t *mbuf)
{
    // session 活跃，将 destory_timer 进行 delay; 积累一定数量之后才进行 delay 操作;
    static const uint64_t kTimerDelayCounterMax = 5;
    if (timerDelayCounter_++ > kTimerDelayCounterMax)
    {
        nxt_timer_delay(getSessionKeeper()->getTimerScheduler(), timerDestroy_, NXT_TIMER_SESSION_EXPIRE);
        timerDelayCounter_ = 0;
    }

    if (NXT_DIR_C2S == direction)
    {
        return flowC2S_.processPacket(engine, mbuf);
    }
    else if (NXT_DIR_S2C == direction)
    {
        return flowS2C_.processPacket(engine, mbuf);
    }
    else
    {
        // TODO: 错误的 direction
        return 0;
    }
}

int nxt_Session::read(nxt_direction_enum direction, uint8_t *buff, int buffLen, nxt_stream_read_res_t *readStatus)
{
    if (NXT_DIR_C2S == direction)
    {
        return flowC2S_.read(buff, buffLen, readStatus);
    }
    else if (NXT_DIR_S2C == direction)
    {
        return flowS2C_.read(buff, buffLen, readStatus);
    }
    else
    {
        // TODO: 错误的 direction
        return 0;
    }
}

int nxt_Session::readToRingbuf(nxt_direction_enum direction, nxt_ringbuf_t *rbuf, uint32_t readLen, nxt_stream_read_res_t *readStatus)
{
    if (NXT_DIR_C2S == direction)
    {
        return flowC2S_.readToRingbuf(rbuf, readLen, readStatus);
    }
    else if (NXT_DIR_S2C == direction)
    {
        return flowS2C_.readToRingbuf(rbuf, readLen, readStatus);
    }
    else
    {
        // TODO: 错误的 direction
        return 0;
    }
}

nxt_session_t*  nxt_SessionKeeper::newSession(nxt_tuple_5_ipv4_t *t5)
{
    nxt_session_t *findSession = this->findSession(t5);
    if (NULL != findSession)
    {
        return findSession;
    }

    auto session = yv::create_object<nxt_Session>(alloc_, this, nextSessionNumber_++, t5);
    auto res     = ipv4SessionTable_.emplace(*t5, session);

    return res.first->second;
}

int  nxt_SessionKeeper::destroySession(nxt_tuple_5_ipv4_t *t5)
{
    nxt_session_t* session = findSession(t5);
    if (NULL == session)
    {
        return -1;
    }

    // 使用 session 中的五元组进行销毁，因为传入的 t5 可能是一个反向的 t5 信息;
    ipv4SessionTable_.erase(session->t5Ipv4_);
    yv::destroy_object<nxt_Session>(alloc_, session);

    return 0;
}

int nxt_SessionKeeper::destroySession(nxt_Session *session)
{
    ipv4SessionTable_.erase(session->t5Ipv4_);
    yv::destroy_object<nxt_Session>(alloc_, session);
    return 0;
}

nxt_SessionKeeper::~nxt_SessionKeeper()
{
    for (auto &item : ipv4SessionTable_)
    {
        yv::destroy_object<nxt_Session>(alloc_, item.second);
    }
}

nxt_session_t* nxt_SessionKeeper::newTcpSession(nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf _U_, nxt_dissector_t *tcpDissector _U_, precord_t *precord _U_)
{
    return newSession(t5);
}

nxt_session_t* nxt_SessionKeeper::newUdpSession(nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf _U_, nxt_dissector_t *udpDissector _U_, precord_t *precord _U_)
{
    return newSession(t5);
}

nxt_tuple_5_ipv4_t nxt_tuple_5_ipv4_reverse(nxt_tuple_5_ipv4_t *t5From)
{
    nxt_tuple_5_ipv4_t t5ToValue, *t5To = &t5ToValue;

    t5To->srcAddr = t5From->dstAddr;
    t5To->dstAddr = t5From->srcAddr;
    t5To->srcPort = t5From->dstPort;
    t5To->dstPort = t5From->srcPort;
    t5To->proto   = t5From->proto;

    return t5ToValue;
}

nxt_session_t* nxt_SessionKeeper::findSession(nxt_tuple_5_ipv4_t *t5)
{
    auto findIter = ipv4SessionTable_.find(*t5);
    if (findIter != ipv4SessionTable_.end())
    {
        return findIter->second;
    }

    nxt_tuple_5_ipv4_t t5Reverse = nxt_tuple_5_ipv4_reverse(t5);
    findIter = ipv4SessionTable_.find(t5Reverse);
    if (findIter != ipv4SessionTable_.end())
    {
        return findIter->second;
    }

    return NULL;
}

nxt_PsudoSession::nxt_PsudoSession(nxt_SessionKeeper *keeper, uint64_t index, nxt_tuple_5_ipv4_t *t5, nxt_ISessionProxy *proxy)
  : nxt_Session(keeper, index, t5)
  , proxy_(proxy)
{

}
