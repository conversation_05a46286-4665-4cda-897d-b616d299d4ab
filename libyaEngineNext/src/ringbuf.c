#include <yaEngineNext/nxt_ringbuf.h>
#include <yaEngineNext/nxt_engine.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

// WARNING: 暂时不进行自动增长;
// WARNING: 不支持多线程同时使用;
typedef struct nxt_Ringbuf
{
    uint32_t      size;
    uint32_t      data_len;
    uint32_t      data_start;
    uint8_t       buf[0];
} nxt_ringbuf_t;

#define RBUF_FREE_BACKSPACE(r)  (r->size - r->data_start - r->data_len)
#define RBUF_FREE_TOTALSPACE(r) (r->size - r->data_len)
#define RBUF_READ_POINT(r)      (r->buf + r->data_start)
#define RBUF_WRITE_POINT(r)     (r->buf + r->data_start + r->data_len)

nxt_ringbuf_t* nxt_ringbuf_create_wa(ya_allocator_t *alloc, uint32_t size)
{
    nxt_ringbuf_t *p = (nxt_ringbuf_t *)ya_allocator_alloc(alloc, (sizeof(nxt_ringbuf_t) + size));
    p->size          = size;
    p->data_start    = 0;
    p->data_len      = 0;
    return p;
}

void nxt_ringbuf_destroy_wa(ya_allocator_t *alloc, nxt_ringbuf_t *rbuf)
{
    ya_allocator_free(alloc, rbuf);
}

nxt_ringbuf_t* nxt_ringbuf_create(uint32_t size)
{
    return nxt_ringbuf_create_wa(ya_allocator_get_default(), size);
}

void nxt_ringbuf_destroy(nxt_ringbuf_t *rbuf)
{
    nxt_ringbuf_destroy_wa(ya_allocator_get_default(), rbuf);
}

static inline
void nxt_ringbuf_compact(nxt_ringbuf_t *rbuf)
{
    memmove(rbuf->buf, rbuf->buf + rbuf->data_start, rbuf->data_len);
    rbuf->data_start = 0;
}

int nxt_ringbuf_push_back(nxt_ringbuf_t *rbuf, const uint8_t *from, uint32_t len)
{
    // buff 剩余空间无法满足需求
    if (RBUF_FREE_TOTALSPACE(rbuf) < len)
    {
        nxt_exception_throw(NXT_EXCEPT_RINGBUF_NO_SPACE, "ringbuf no space");
        return -1;
    }

    if (RBUF_FREE_BACKSPACE(rbuf) < len)
    {   // 尾部空间不足以存储下 len 内容, 需要进行 memmove
        nxt_ringbuf_compact(rbuf);
    }

    memcpy(RBUF_WRITE_POINT(rbuf), from, len);
    rbuf->data_len += len;
    return len;
}

int nxt_ringbuf_pop_front(nxt_ringbuf_t *rbuf, uint32_t len)
{
    if (len > rbuf->data_len)
    {
        nxt_exception_throw(NXT_EXCEPT_RINGBUF_NO_ENOUGH_DATA, "ringbuf no enough data");
        return -1;
    }

    rbuf->data_start += len;
    rbuf->data_len    -= len;

    return rbuf->data_len;
}

uint8_t* nxt_ringbuf_get_data(nxt_ringbuf_t *rbuf)
{
    return RBUF_READ_POINT(rbuf);
}

uint32_t nxt_ringbuf_get_data_length(nxt_ringbuf_t *rbuf)
{
    return rbuf->data_len;
}

uint32_t nxt_ringbuf_get_free_size(nxt_ringbuf_t *rbuf)
{
    return RBUF_FREE_TOTALSPACE(rbuf);
}
