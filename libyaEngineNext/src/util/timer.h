#ifndef NXT_TIMER_H
#define NXT_TIMER_H

#include <yaBasicUtils/allocator.h>
#include <stdint.h>

typedef struct nxt_TimerScheduler nxt_timer_scheduler_t;
typedef struct nxt_Timer           nxt_timer_t;

typedef void (*nxt_timer_callback_t)(nxt_timer_t *timer, void *userdata);

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// 一个单线程使用的定时器实现
nxt_timer_scheduler_t* nxt_timer_scheduler_create(ya_allocator_t *alloc,  uint32_t precisionInMs);
int                    nxt_timer_scheduler_destroy(ya_allocator_t *alloc,  nxt_timer_scheduler_t* scheduler);
int                    nxt_timer_scheduler_update(nxt_timer_scheduler_t* scheduler, uint32_t currentInMs);
int                    nxt_timer_scheduler_reset(nxt_timer_scheduler_t* scheduler);

nxt_timer_t*           nxt_timer_create(ya_allocator_t *alloc, nxt_timer_scheduler_t* scheduler, nxt_timer_callback_t callback, void *userdata);
int                    nxt_timer_destroy(ya_allocator_t *alloc, nxt_timer_scheduler_t* scheduler, nxt_timer_t* timer);

// repeat 表示重复次数，-1 表示无限重复
int                    nxt_timer_start(nxt_timer_scheduler_t* scheduler, nxt_timer_t *timer, uint32_t expiresAfterNMs, int repeat);
int                    nxt_timer_stop(nxt_timer_scheduler_t* scheduler, nxt_timer_t *timer);
int                    nxt_timer_delay(nxt_timer_scheduler_t* scheduler, nxt_timer_t *timer, uint32_t delayMs);

#ifdef __cplusplus
}
#endif

#endif /* NXT_TIMER_H */
