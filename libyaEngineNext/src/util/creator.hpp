#ifndef ICREATOR_H
#define ICREATOR_H

#include <yaBasicUtils/allocator.h>
#include <boost/container/pmr/global_resource.hpp>
#include <boost/container/pmr/unsynchronized_pool_resource.hpp>
#include <boost/container/pmr/monotonic_buffer_resource.hpp>
#include <boost/container/pmr/polymorphic_allocator.hpp>
#include <vector>
#include <string.h>

namespace pmr = boost::container::pmr;

namespace yv {

// basic
template<typename T>
class nxt_BasicCreator
{
public:
    template<typename... Args>
    T* create(Args... args)
    {
        return new T(args...);
    }

    void destroy(T *p)
    {
        delete p;
    }
};

// fast
template<typename T>
class nxt_PoolCreator
{
public:
    nxt_PoolCreator()
        : resource_()
        , alloc_(&resource_)
    {
        // prealloc
        std::vector<T*> array(20000);
        for (auto &v : array)
        {
            v = alloc_.allocate(1);
            memset((void *)v, 0, sizeof (T));
        }

        for (auto &v : array)
        {
            alloc_.deallocate(v, 1);
        }
    }

public:
    template<typename... Args>
    T* create(Args... args)
    {
        auto *p = alloc_.allocate(1);
        alloc_.construct(p, args...);
        return p;
    }

    void destroy(T *p)
    {
        p->~T();
        alloc_.deallocate(p, 1);
    }

private:
    pmr::unsynchronized_pool_resource resource_;
    pmr::polymorphic_allocator<T> alloc_;
};

template<typename T>
class nxt_ArenaCreator
{
public:
    nxt_ArenaCreator(pmr::memory_resource *upstream)
        : arena_(upstream)
        , alloc_(&arena_)
    {
    }

public:
    template<typename... Args>
    T* create(Args... args)
    {
        auto *p = alloc_.allocate(1);
        alloc_.construct(p, args...);
        return p;
    }

    void destroy(T *)
    {
        // p->~T();
        // alloc_.deallocate(p, 1);
    }

private:
    pmr::monotonic_buffer_resource arena_;
    pmr::polymorphic_allocator<T> alloc_;
};

};
#endif /* ICREATOR_H */
