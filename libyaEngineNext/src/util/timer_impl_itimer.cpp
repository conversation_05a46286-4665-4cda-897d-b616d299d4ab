#include "timer.h"
#include "itimer.h"
#include "yaEngineNext/nxt_util.h"
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/allocator.hpp>
#include <sys/time.h>

typedef void (*nxt_timer_callback_itimer_t)(void *timer, void *userdata);

struct nxt_TimerScheduler
{
public:
    friend struct nxt_Timer;

public:
    nxt_TimerScheduler(uint32_t precisionInMs)
    {
        itimer_mgr_init(&mgr_, precisionInMs);
    }

    void update(uint32_t currentInMs)
    {
        itimer_mgr_run(&mgr_, currentInMs);
    }

    void reset()
    {
        itimer_mgr_destroy(&mgr_);
    }

private:
    itimer_mgr mgr_;
};

struct nxt_Timer
{
public:
    nxt_Timer(nxt_timer_callback_t callback, void *userdata)
    {
        itimer_evt_init(&evt_, (nxt_timer_callback_itimer_t)callback, this, userdata);
    }

    int start(nxt_TimerScheduler* scheduler, uint32_t expiresAfterNMs, int repeat)
    {
        itimer_evt_start(&scheduler->mgr_, &evt_, expiresAfterNMs, repeat);
        return 0;
    }

    int stop(nxt_TimerScheduler* scheduler)
    {
        itimer_evt_stop(&scheduler->mgr_, &evt_);
        return 0;
    }

    int delay(nxt_timer_scheduler_t* scheduler, uint32_t delayMs)
    {
        itimer_evt_stop(&scheduler->mgr_, &evt_);
        itimer_evt_start(&scheduler->mgr_, &evt_, delayMs, 1);
        return 0;
    }

private:
    itimer_evt           evt_;
};

nxt_timer_scheduler_t* nxt_timer_scheduler_create(ya_allocator_t *alloc, uint32_t precisionInMs)
{
    return yv::create_object<nxt_timer_scheduler_t>(alloc, precisionInMs);
}

int nxt_timer_scheduler_destroy(ya_allocator_t *alloc, nxt_timer_scheduler_t* scheduler)
{
    yv::destroy_object(alloc, scheduler);
    return 0;
}

int nxt_timer_scheduler_update(nxt_timer_scheduler_t* scheduler, uint32_t currentInMs)
{
    scheduler->update(currentInMs);
    return 0;
}

int nxt_timer_scheduler_reset(nxt_timer_scheduler_t* scheduler)
{
    scheduler->reset();
    return 0;
}

nxt_timer_t* nxt_timer_create(ya_allocator_t *alloc, nxt_timer_scheduler_t* scheduler _U_, nxt_timer_callback_t callback, void *userdata)
{
    return yv::create_object<nxt_Timer>(alloc, callback, userdata);
}

int nxt_timer_destroy(ya_allocator_t *alloc, nxt_timer_scheduler_t* scheduler _U_, nxt_timer_t* timer)
{
    yv::destroy_object<nxt_timer_t>(alloc, timer);
    return 0;
}

int nxt_timer_start(nxt_timer_scheduler_t* scheduler , nxt_timer_t *timer, uint32_t expiresAfterNMs, int repeat)
{
    timer->start(scheduler, expiresAfterNMs, repeat);
    return 0;
}

int nxt_timer_stop(nxt_timer_scheduler_t* scheduler , nxt_timer_t *timer)
{
    return timer->stop(scheduler);
}

int nxt_timer_delay(nxt_timer_scheduler_t* scheduler , nxt_timer_t *timer, uint32_t delayMs)
{
    return timer->delay(scheduler, delayMs);
}

uint32_t nxt_utils_current_time_ms()
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return tv.tv_sec * 1000 + tv.tv_usec / 1000;
}
