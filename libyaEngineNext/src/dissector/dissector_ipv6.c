#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>

#define PROTO_NAME "ipv6"

// IPv6 Next Header values (same as IPv4 protocol numbers for common protocols)
#define IPV6_NEXT_HEADER_HOPBYHOP    0   // Hop-by-Hop Options Header
#define IPV6_NEXT_HEADER_ICMPV6      58  // ICMPv6
#define IPV6_NEXT_HEADER_TCP         6   // TCP
#define IPV6_NEXT_HEADER_UDP         17  // UDP
#define IPV6_NEXT_HEADER_ROUTING     43  // Routing Header
#define IPV6_NEXT_HEADER_FRAGMENT    44  // Fragment Header
#define IPV6_NEXT_HEADER_DSTOPTS     60  // Destination Options Header
#define IPV6_NEXT_HEADER_NONE        59  // No Next Header

static const char* ipv6_next_header_name(uint8_t next_header)
{
    switch (next_header) {
        case IPV6_NEXT_HEADER_HOPBYHOP:  return "Hop-by-Hop Options";
        case IPV6_NEXT_HEADER_ICMPV6:    return "ICMPv6";
        case IPV6_NEXT_HEADER_TCP:       return "TCP";
        case IPV6_NEXT_HEADER_UDP:       return "UDP";
        case IPV6_NEXT_HEADER_ROUTING:   return "Routing Header";
        case IPV6_NEXT_HEADER_FRAGMENT:  return "Fragment Header";
        case IPV6_NEXT_HEADER_DSTOPTS:   return "Destination Options";
        case IPV6_NEXT_HEADER_NONE:      return "No Next Header";
        default:                         return "Unknown";
    }
}

static
int ipv6_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // Check minimum IPv6 header length (40 bytes)
    if (nxt_mbuf_get_length(mbuf) < 40) {
        printf("IPv6: insufficient data length (%d bytes, need at least 40)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse IPv6 header
    uint32_t version_tc_fl = nxt_mbuf_get_uint32_ntoh(mbuf, 0);
    uint8_t version = (version_tc_fl >> 28) & 0xF;
    uint8_t traffic_class = (version_tc_fl >> 20) & 0xFF;
    uint32_t flow_label = version_tc_fl & 0xFFFFF;
    
    uint16_t payload_length = nxt_mbuf_get_uint16_ntoh(mbuf, 4);
    uint8_t next_header = nxt_mbuf_get_uint8(mbuf, 6);
    uint8_t hop_limit = nxt_mbuf_get_uint8(mbuf, 7);

    // Source and destination addresses (16 bytes each)
    const uint8_t *src_addr = nxt_mbuf_get_raw(mbuf, 8);
    const uint8_t *dst_addr = nxt_mbuf_get_raw(mbuf, 24);

    // Validate version
    if (version != 6) {
        printf("IPv6: invalid version (%d, expected 6)\n", version);
        return -1;
    }

    // Record IPv6 fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "traffic_class", uinteger, traffic_class);
    precord_put(precord, "flow_label", uinteger, flow_label);
    precord_put(precord, "payload_length", uinteger, payload_length);
    precord_put(precord, "next_header", uinteger, next_header);
    precord_put(precord, "next_header_name", string, ipv6_next_header_name(next_header));
    precord_put(precord, "hop_limit", uinteger, hop_limit);
    precord_put(precord, "src", bytes, src_addr, 16);
    precord_put(precord, "dst", bytes, dst_addr, 16);

    // Put IPv6 info to engine regzone for session tracking
    // Note: We'll use a simplified approach for IPv6 addresses in the engine
    // by mapping them to IPv4 format using hash values
    uint32_t src_hash = 0, dst_hash = 0;
    for (int i = 0; i < 16; i += 4) {
        src_hash ^= nxt_mbuf_get_uint32_ntoh(mbuf, 8 + i);
        dst_hash ^= nxt_mbuf_get_uint32_ntoh(mbuf, 24 + i);
    }
    // Use existing IPv4 infrastructure with hash values
    nxt_engine_pktzone_put_ipv4(engine, next_header, src_hash, dst_hash);

    // Set total length for layer processing
    nxt_engine_layer_set_total_len(engine, 40 + payload_length);

    // Put basic layer info
    precord_put_to_layer(precord, "basic", "SrcIp", bytes, src_addr, 16);
    precord_put_to_layer(precord, "basic", "DstIp", bytes, dst_addr, 16);

    printf("IPv6: Version=%d, TC=%d, FL=%d, PayloadLen=%d, NextHdr=%s (%d), HopLimit=%d\n", 
           version, traffic_class, flow_label, payload_length, 
           ipv6_next_header_name(next_header), next_header, hop_limit);

    // Set handoff key for next protocol
    nxt_handoff_set_key_of_number(engine, next_header);
    
    return 40; // IPv6 header length
}

static
int ipv6_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "ipv6");
    
    pschema_register_field(pschema, "version", YA_FT_UINT8, "IP version");
    pschema_register_field_ex(pschema, "traffic_class", YA_FT_UINT8, "traffic class", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "flow_label", YA_FT_UINT32, "flow label", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "payload_length", YA_FT_UINT16, "payload length");
    pschema_register_field(pschema, "next_header", YA_FT_UINT8, "next header");
    pschema_register_field(pschema, "next_header_name", YA_FT_STRING, "next header name");
    pschema_register_field(pschema, "hop_limit", YA_FT_UINT8, "hop limit");
    pschema_register_field(pschema, "src", YA_FT_BYTES, "source IPv6 address");
    pschema_register_field(pschema, "dst", YA_FT_BYTES, "destination IPv6 address");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "ipv6",
    .schemaRegFun = ipv6_schema_reg,
    .dissectFun   = ipv6_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("eth", 0x86DD),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(ipv6)
{
    nxt_dissector_register(&gDissectorDef);
}
