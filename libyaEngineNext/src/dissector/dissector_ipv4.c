#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "ipv4"

static
int ipv4_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    uint16_t totalLen = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint16_t id       = nxt_mbuf_get_uint16_ntoh(mbuf, 4);
    uint8_t  proto    = nxt_mbuf_get_uint8(mbuf, 9);
    uint32_t srcIp    = nxt_mbuf_get_uint32(mbuf, 12);
    uint32_t dstIp    = nxt_mbuf_get_uint32(mbuf, 16);

    // put ip info to engine regzone;
    nxt_engine_pktzone_put_ipv4(engine, proto, srcIp, dstIp);

    // ip 层可以知道它的 layer total len, 进行设置;
    // 其它层将整个 mbuf 的 remain 当作自己的 layer range;
    nxt_engine_layer_set_total_len(engine, totalLen);

    precord_put(precord, "total_len", uinteger, totalLen);
    precord_put(precord, "id",        uinteger, id);
    precord_put(precord, "proto",     uinteger, proto);
    precord_put(precord, "src",       ipv4, srcIp);
    precord_put(precord, "dst",       ipv4, dstIp);

    precord_put_to_layer(precord, "basic", "SrcIp", ipv4, srcIp);
    precord_put_to_layer(precord, "basic", "DstIp", ipv4, dstIp);

    nxt_handoff_set_key_of_number(engine, proto);
    return 20; // TODO: 需要计算 ip 头长度
}

static
int ipv4_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "ipv4");
     pschema_register_field(pschema    , "total_len", YA_FT_UINT16, "total length");
     pschema_register_field_ex(pschema , "id",        YA_FT_UINT16, "identification", YA_DISPLAY_BASE_HEX);
     pschema_register_field(pschema    , "src",       YA_FT_IPv4,   "src ipv4 address");
     pschema_register_field(pschema    , "dst",       YA_FT_IPv4,   "dst ipv4 address");
     pschema_register_field(pschema    , "proto",     YA_FT_UINT8,  "ip protocol");

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "ipv4",
    .schemaRegFun = ipv4_schema_reg,
    .dissectFun   = ipv4_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("eth", 0x0800),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(ipv4)
{
    nxt_dissector_register(&gDissectorDef);
}
