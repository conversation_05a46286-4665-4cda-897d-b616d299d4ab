#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_ringbuf.h"
#include "yaEngineNext/nxt_parser.h"
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/macro.h>

#define PROTO_NAME "rtsp"

typedef struct rtspSessionUserdata
{
    nxt_parser_t  *parser;
    nxt_ringbuf_t *rbuf;
    precord_t     *precord;
} rtsp_session_userdata_t;

static
void rtsp_uerdata_init(nxt_session_t *session _U_, void *userdata)
{
    rtsp_session_userdata_t *u     = (rtsp_session_userdata_t *)userdata;
    ya_allocator_t          *alloc = nxt_session_get_allocator(NULL, session);
    u->parser = nxt_parser_create_wa(alloc, nxt_parser_rtsp_init);
    u->rbuf   = nxt_ringbuf_create_wa(alloc, 4000);
}

static
void rtsp_uerdata_finish(nxt_session_t *session _U_, void *userdata)
{
    rtsp_session_userdata_t *u     = (rtsp_session_userdata_t *)userdata;
    ya_allocator_t          *alloc = nxt_session_get_allocator(NULL, session);
    nxt_parser_destroy_wa(alloc, u->parser);
    nxt_ringbuf_destroy_wa(alloc, u->rbuf);
}

static
int rtsp_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf _U_)
{
    rtsp_session_userdata_t *u = (rtsp_session_userdata_t *)nxt_session_get_userdata(engine, session);
    int gotLen = nxt_session_stream_rbread(engine, session, NXT_DIR_C2S, u->rbuf, 0, NULL);
    if (gotLen == 0)
    {
        return 0;
    }

    // 读取了数据，并且 parser 为 ready 状态(不处于一个消息解析中间)，创建一个新的 precord;
    if (nxt_parser_get_status(u->parser) == NXT_PSTATUS_READY)
    {
        u->precord = nxt_session_create_record(engine, session); // TODO: session 应该对 precord 进行管理;
        precord_layer_put_new_layer_cache(u->precord, PROTO_NAME);
    }

    // 对 ringbuff 中的数据进行 parse
    int consume = nxt_parser_rtsp_parse(u->parser, nxt_ringbuf_get_data(u->rbuf), nxt_ringbuf_get_data_length(u->rbuf), u->precord);
    if (consume < 0)
    {   // WARNING: 如果解析失败，此时的 precord 该如何处理?
        return -1;
    }

    // 从 ringbuf 中 pop 已经消耗掉的部分, 使得下次调用 nxt_ringbuf_get_data 拿到下一批数据;
    nxt_ringbuf_pop_front(u->rbuf, consume);

    // 成功解析出一条消息，调整 buffer, 发布消息，将 parser 进行 reset;
    if (nxt_parser_get_status(u->parser) == NXT_PSTATUS_COMPLETE)
    {
        nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, u->precord); // precord 会被销毁;
        nxt_session_destroy_record(engine, session, u->precord);
        nxt_parser_reset(u->parser);
    }

    return consume;
}

static
int rtsp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "rtsp");

     // 说明: 以下字段未找到相关定义，不提供
     // ArchiveViewingLimitation, ArchiveViewingLimitation2,
     // cookie, referer, vary

     pschema_register_field(pschema, "method",             YA_FT_STRING,  ""); // >>ragel action
     pschema_register_field(pschema, "version",            YA_FT_STRING,  "");
     pschema_register_field(pschema, "uri",                YA_FT_STRING,  "");
     pschema_register_field(pschema, "status",             YA_FT_STRING,  "");
     pschema_register_field(pschema, "reason_phrase",      YA_FT_STRING,  "response reason phrase.");
     pschema_register_field(pschema, "accept",             YA_FT_STRING,  "");
     pschema_register_field(pschema, "accept_charset",     YA_FT_STRING,  ""); // TODO: accept_charset 在语法中似乎没有定义为一个 header;
     pschema_register_field(pschema, "accept_encoding",    YA_FT_STRING,  "");
     pschema_register_field(pschema, "accept_language",    YA_FT_STRING,  "");
     pschema_register_field(pschema, "allow",              YA_FT_STRING,  "");
     pschema_register_field(pschema, "authorization",      YA_FT_STRING,  "");
     pschema_register_field(pschema, "bandwidth",          YA_FT_STRING,  "");
     pschema_register_field(pschema, "blocksize",          YA_FT_STRING,  "");
     pschema_register_field(pschema, "cache_control",      YA_FT_STRING,  "");
     pschema_register_field(pschema, "content_base",       YA_FT_STRING,  "");
     pschema_register_field(pschema, "content_encoding",   YA_FT_STRING,  "");
     pschema_register_field(pschema, "content_language",   YA_FT_STRING,  "");
     pschema_register_field(pschema, "content_length",     YA_FT_STRING,  "");
     pschema_register_field(pschema, "content_location",   YA_FT_STRING,  "");
     pschema_register_field(pschema, "content_type",       YA_FT_STRING,  "");
     pschema_register_field(pschema, "cseq",               YA_FT_STRING,  "");
     pschema_register_field(pschema, "date",               YA_FT_STRING,  "");
     pschema_register_field(pschema, "expires",            YA_FT_STRING,  "");
     pschema_register_field(pschema, "from",               YA_FT_STRING,  "");
     pschema_register_field(pschema, "if_match",           YA_FT_STRING,  "");
     pschema_register_field(pschema, "if_modified_since",  YA_FT_STRING,  "");
     pschema_register_field(pschema, "last_modified",      YA_FT_STRING,  "");
     pschema_register_field(pschema, "location",           YA_FT_STRING,  "");
     pschema_register_field(pschema, "proxy_authenticate", YA_FT_STRING,  "");
     pschema_register_field(pschema, "proxy_require",      YA_FT_STRING,  "");
     pschema_register_field(pschema, "public",             YA_FT_STRING,  "");
     pschema_register_field(pschema, "range",              YA_FT_STRING,  "");
     pschema_register_field(pschema, "retry_after",        YA_FT_STRING,  "");
     pschema_register_field(pschema, "require",            YA_FT_STRING,  "");
     pschema_register_field(pschema, "rtp_info",           YA_FT_STRING,  "");
     pschema_register_field(pschema, "scale",              YA_FT_STRING,  "");
     pschema_register_field(pschema, "speed",              YA_FT_STRING,  "");
     pschema_register_field(pschema, "server",             YA_FT_STRING,  "");
     pschema_register_field(pschema, "session",            YA_FT_STRING,  "");
     pschema_register_field(pschema, "supported",          YA_FT_STRING,  "");
     pschema_register_field(pschema, "timestamp",          YA_FT_STRING,  "");
     pschema_register_field(pschema, "transport",          YA_FT_STRING,  "");
     pschema_register_field(pschema, "unsupported",        YA_FT_STRING,  "");
     pschema_register_field(pschema, "user_agent",         YA_FT_STRING,  "");
     pschema_register_field(pschema, "via",                YA_FT_STRING,  "");
     pschema_register_field(pschema, "www_authenticate",   YA_FT_STRING,  "");
     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rtsp",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = rtsp_schema_reg,
    .dissectFun   = rtsp_dissect,
    .userdata     = {sizeof (rtsp_session_userdata_t), rtsp_uerdata_init, rtsp_uerdata_finish},
    .mountAt      = {
        NXT_MNT_PORT_PAYLOAD("tcp", 554, "^SETUP "),
        NXT_MNT_PORT_PAYLOAD("tcp", 554, "^DESCRIBE "),
        NXT_MNT_PORT_PAYLOAD("tcp", 554, "^OPTIONS "),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rtsp)
{
    nxt_dissector_register(&gDissectorDef);
}
