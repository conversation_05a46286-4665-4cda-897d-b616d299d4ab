#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_parser.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>

#include <stdlib.h>

#define PROTO_NAME "http"

int nxt_parser_http_init(nxt_parser_t *parser);
int nxt_parser_http_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata);

typedef enum http_parsing_state
{
    HTTP_PARSING_STATE_ERROR = 0,
    HTTP_PARSING_STATE_NEW_MSG,                       // 开始了一个新的 http 消息;
    HTTP_PARSING_STATE_PARSING_MSG_HEADER,            // 解析 http 消息头;
    HTTP_PARSING_STATE_PARSING_MSG_BODY,              // 解析 http body;
} http_parsing_state_enum;

typedef enum http_body_type
{
    HTTP_BODY_TYPE_UNKNOWN = 0,
    HTTP_BODY_TYPE_NO_BODY,
    HTTP_BODY_TYPE_CONTENT_LENGTH,                    // 通过 Content-Length 说明 body 长度;
    HTTP_BODY_TYPE_CHUNKED,                           // 使用 chunked 方式传输 body;
    HTTP_BODY_TYPE_UNTIL_CLOSE,                       // 通过关闭连接来标识 body 发送完成，
                                                      // 通常出现在 http 1.0 响应场景，server 可能不会通过 Content-Length 说明长度;
} http_body_type_enum;

typedef struct httpParserContext
{
    nxt_direction_enum       dir;
    nxt_parser_t            *parser;
    nxt_ringbuf_t           *rbuf;
    precord_t               *precord;
    http_parsing_state_enum  pstate;
    http_body_type_enum      bodyType;
    uint32_t                 bodyRemain;
    uint32_t                 msgNumber; // Message Number in Stream
} http_parser_ctx_t;

typedef struct httpSessionUserdata
{
    http_parser_ctx_t request;
    http_parser_ctx_t response;
} http_session_userdata_t;

static
void http_parser_ctx_init(nxt_session_t *session, http_parser_ctx_t *ctx)
{
    ya_allocator_t *alloc = nxt_session_get_allocator(NULL, session);

    ctx->parser     = nxt_parser_create_wa(alloc, nxt_parser_http_init);
    ctx->rbuf       = nxt_ringbuf_create_wa(alloc, 4000);
    ctx->precord    = NULL;
    ctx->pstate     = HTTP_PARSING_STATE_NEW_MSG;
    ctx->bodyType   = HTTP_BODY_TYPE_UNKNOWN;
    ctx->bodyRemain = 0;
    ctx->msgNumber  = 0;
}

static
void http_parser_ctx_finish(nxt_session_t *session, http_parser_ctx_t *ctx)
{
    if (ctx->precord)
    {   // 有可能 ctx->pstatus 不为 ERROR 但是后续会话结束不再有数据,
        // 但是 ctx->precord 被残留，只能在会话结束时进行销毁;
        nxt_session_destroy_record(NULL, session, ctx->precord);
    }

    ya_allocator_t *alloc = nxt_session_get_allocator(NULL, session);

    nxt_parser_destroy_wa(alloc, ctx->parser);
    nxt_ringbuf_destroy_wa(alloc, ctx->rbuf);
}

static
void http_uerdata_init(nxt_session_t *session, void *userdata)
{
    http_session_userdata_t *u = (http_session_userdata_t *)userdata;
    http_parser_ctx_init(session, &u->request);  u->request.dir  = NXT_DIR_C2S;
    http_parser_ctx_init(session, &u->response); u->response.dir = NXT_DIR_S2C;
}

static
void http_uerdata_finish(nxt_session_t *session _U_, void *userdata)
{
    http_session_userdata_t *u = (http_session_userdata_t *)userdata;
    http_parser_ctx_finish(session, &u->request);
    http_parser_ctx_finish(session, &u->response);
}

static
void http_on_parsing_msg_complete(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf, http_parser_ctx_t *ctx)
{
    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, ctx->precord); // precord 会被销毁;
    nxt_session_destroy_record(engine, session, ctx->precord);
    ctx->precord = NULL;
    nxt_parser_reset(ctx->parser);

    // msn 号递增
    ctx->msgNumber++;
}

// 从 ctx->rbuf 中解析 http 协议，直到 ringbuf 中数据处理完成
// 或者 parser 解析出错，或者当前数据不够 parser 继续解析(例如 parser 返回 NXT_PSTATUS_PARTIAL);
static
int http_dissect_message_in_ringbuf(nxt_engine_t *engine, nxt_session_t *session,
                                    nxt_direction_enum dir _U_, nxt_mbuf_t *mbuf, http_parser_ctx_t *ctx)
{
    int totalLen   = nxt_ringbuf_get_data_length(ctx->rbuf);
    int consumeLen = 0;

    while (consumeLen < totalLen)
    {
        switch (ctx->pstate)
        {
        case HTTP_PARSING_STATE_ERROR:
        {   // TODO:
            // 考虑是否将 ERROR 报告出去进行通知，例如实现 error stream 的 dump;
            // 考虑是否将 ERROR 状态反馈到 tcp 层，例如不再继续进行重组;
            int remainLen    = nxt_ringbuf_get_data_length(ctx->rbuf);
            nxt_ringbuf_pop_front(ctx->rbuf, remainLen);
            if (ctx->precord)
            {
                /* printf("http parse error, %s\n", nxt_session_get_str_repr(engine, session)); */
                nxt_session_destroy_record(engine, session, ctx->precord);
                ctx->precord = NULL;
            }
            consumeLen = -1;
            goto OUT;
        }

        case HTTP_PARSING_STATE_NEW_MSG:
        {   // NEW_MSG, 创建一个新的 precord;
            ctx->precord = nxt_session_create_record(engine, session);   // TODO: session 应该对 precord 进行管理;
            precord_layer_put_new_layer_cache(ctx->precord, PROTO_NAME);

            precord_put(ctx->precord, "Msg-Number", uinteger, ctx->msgNumber);
            ctx->pstate = HTTP_PARSING_STATE_PARSING_MSG_HEADER;        // 状态迁移
            break;
        }

        case HTTP_PARSING_STATE_PARSING_MSG_HEADER:
        {   // parsing 消息头
            int processLen = nxt_parser_http_parse(ctx->parser, nxt_ringbuf_get_data(ctx->rbuf),
                                                    nxt_ringbuf_get_data_length(ctx->rbuf), ctx->precord);
            if (processLen < 0             // parse 出错
                || nxt_parser_get_status(ctx->parser) == NXT_PSTATUS_ERROR)
            {   // WARNING: 如果解析失败，此时的 precord 该如何处理?
                ctx->pstate = HTTP_PARSING_STATE_ERROR;                 // 状态迁移
                break;
            }

            // 从 ringbuf 中 pop 已经消耗掉的部分, 使得下次调用 nxt_ringbuf_get_data 拿到下一批数据;
            consumeLen += processLen;
            nxt_ringbuf_pop_front(ctx->rbuf, processLen);

            // 消息部分解析
            if (nxt_parser_get_status(ctx->parser) == NXT_PSTATUS_PARTIAL)
            {   // ringbuf 中的数据内容不足，需要更多数据才能继续进行;
                goto OUT;
            }

            // 解析完成消息头;
            if (nxt_parser_get_status(ctx->parser) == NXT_PSTATUS_COMPLETE
                && ctx->precord)
            {
                ya_fvalue_t* fvContentLength = precord_fvalue_get(ctx->precord, "Content-Length");
                if (NULL == fvContentLength)
                {   // 没有 body, 一个完整的 http 消息解析完成
                    http_on_parsing_msg_complete(engine, session, mbuf, ctx);
                    ctx->pstate = HTTP_PARSING_STATE_NEW_MSG;           // 状态迁移
                    break;
                }

                // 存在 body, content-length 类型
                ctx->bodyType   = HTTP_BODY_TYPE_CONTENT_LENGTH;
                ctx->bodyRemain = atoi(ya_fvalue_get_string(fvContentLength));

                // WARNING:
                // "Content-Length: 0", 此时不能进入 HTTP_PARSING_STATE_PARSING_MSG_BODY,
                // 因为数据可能已经都处理完毕，无法再进入状态机了,
                // 这将会导致 ctx->precord 残留(创建了但未 POST), 从而发生内存泄漏;
                // ctx->precord 最终会在 http_uerdata_finish 时被销毁
                if (ctx->bodyRemain == 0)
                {
                    http_on_parsing_msg_complete(engine, session, mbuf, ctx);
                    ctx->pstate = HTTP_PARSING_STATE_NEW_MSG;           // 状态迁移
                    break;
                }

                ctx->pstate = HTTP_PARSING_STATE_PARSING_MSG_BODY;      // 状态迁移
                break;
            }

            break;
        }
        case HTTP_PARSING_STATE_PARSING_MSG_BODY:
        {
            uint32_t gotLen     = nxt_ringbuf_get_data_length(ctx->rbuf);
            uint32_t processLen = gotLen > ctx->bodyRemain ? ctx->bodyRemain : gotLen;

            // skip body
            consumeLen      += processLen;
            ctx->bodyRemain -= processLen;
            nxt_ringbuf_pop_front(ctx->rbuf, processLen);

            if (ctx->bodyRemain == 0)
            {   // body 处理完成, 也说明一个完整的 http 消息解析完成;
                http_on_parsing_msg_complete(engine, session, mbuf, ctx);
                ctx->pstate = HTTP_PARSING_STATE_NEW_MSG;               // 状态迁移
            }

            break;
        }

        default:
            break;
        }
    }

OUT:
    return consumeLen;
}

// 将 http session 中 dir 方向的数据尽可能地处理，可能解析产生多条消息;
// TODO: 考虑一个会话进入到 ERROR state 之后，有新数据到达时的处理;
int http_process_session_data(nxt_engine_t *engine, nxt_session_t *session, nxt_direction_enum dir, nxt_mbuf_t *mbuf, http_parser_ctx_t *ctx)
{
    // 从 session 中读取数据到 ctx->rbuf 中, 一直"读取, 解析"，
    int consumeLen = 0;
    while (nxt_session_stream_rbread(engine, session, dir, ctx->rbuf, 0, NULL) > 0)
    {
        int processLen = http_dissect_message_in_ringbuf(engine, session, dir, mbuf, ctx);
        if (processLen < 0)
        {
            break;
        }

        consumeLen += processLen;
    }

    return consumeLen;
}

static
int http_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
    http_session_userdata_t *udata = (http_session_userdata_t *)nxt_session_get_userdata(engine, session);
    nxt_direction_enum           dir   = nxt_engine_regzone_get_direction(engine);
    if (NXT_DIR_C2S == dir)
    {
        return http_process_session_data(engine, session, NXT_DIR_C2S, mbuf, &udata->request);
    }
    else if (NXT_DIR_S2C == dir)
    {
        return http_process_session_data(engine, session, NXT_DIR_S2C, mbuf, &udata->response);
    }
    else
    {
        // error
    }

    return 0;
}

static
int http_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto_ex(db, PROTO_NAME, "http");
    pschema_register_field(pschema, "Msg-Number",        YA_FT_UINT32,  "");
    pschema_register_field(pschema, "Method",            YA_FT_STRING,  "");
    pschema_register_field(pschema, "URI",               YA_FT_STRING,  "");
    pschema_register_field(pschema, "Version",           YA_FT_STRING,  "");
    pschema_register_field(pschema, "Status",            YA_FT_STRING,  "");
    pschema_register_field(pschema, "Cache-Control",     YA_FT_STRING,  "");
    pschema_register_field(pschema, "Connection",        YA_FT_STRING,  "");
    pschema_register_field(pschema, "Cookie",            YA_FT_STRING,  "");
    pschema_register_field(pschema, "Cookie2",           YA_FT_STRING,  "");
    pschema_register_field(pschema, "Date",              YA_FT_STRING,  "");
    pschema_register_field(pschema, "Pragma",            YA_FT_STRING,  "");
    pschema_register_field(pschema, "Trailer",           YA_FT_STRING,  "");
    pschema_register_field(pschema, "Transfer-Encoding", YA_FT_STRING,  "");
    pschema_register_field(pschema, "Upgrade",           YA_FT_STRING,  "");
    pschema_register_field(pschema, "Via",               YA_FT_STRING,  "");
    pschema_register_field(pschema, "Warning",           YA_FT_STRING,  "");
    pschema_register_field(pschema, "Accept",            YA_FT_STRING,  "");
    pschema_register_field(pschema, "Accept-Charset",    YA_FT_STRING,  "");
    pschema_register_field(pschema, "Accept-Encoding",   YA_FT_STRING,  "");
    pschema_register_field(pschema, "Accept-Language",   YA_FT_STRING,  "");
    pschema_register_field(pschema, "Authorization",     YA_FT_STRING,  "");
    pschema_register_field(pschema, "Expect",            YA_FT_STRING,  "");
    pschema_register_field(pschema, "Host",              YA_FT_STRING,  "");
    pschema_register_field(pschema, "User-Agent",        YA_FT_STRING,  "");
    pschema_register_field(pschema, "Content-Length",    YA_FT_STRING,  "");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "http",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = http_schema_reg,
    .dissectFun   = http_dissect,
    .userdata     = {sizeof (http_session_userdata_t), http_uerdata_init, http_uerdata_finish},
    .mountAt      = {
        NXT_MNT_PORT_PAYLOAD("tcp", NXT_ANY_PORT, "^GET "),
        NXT_MNT_PORT_PAYLOAD("tcp", NXT_ANY_PORT, "^POST "),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(http)
{
    nxt_dissector_register(&gDissectorDef);
}
