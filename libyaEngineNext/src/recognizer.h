#ifndef PROTORECOGNIZER_H
#define PROTORECOGNIZER_H

#include "dissector.h"
#include "util/noncopyable.h"
#include <vector>
#include <memory>

class nxt_RegexBlock;

class nxt_Recognizer : public nxt::noncopyable
{
public:
    enum nxt_recog_type
    {
        NXT_RECOG_TYPE_NONE = 0,
        NXT_RECOG_TYPE_PORT_ONLY,         // 由端口直接对应到协议;
        NXT_RECOG_TYPE_PORT_AND_PAYLOAD,  // 需要由 payload 匹配得到协议;
    };

public:
    nxt_Recognizer();
    nxt_Recognizer(nxt_Dissector *directorProto);
    ~nxt_Recognizer();

    nxt_recog_type getRecogType()
    {
        return type_;
    }

public: // direct
    nxt_Dissector* getDirectProto()
    {
        return directProto_;
    }

public: // for payload recognize
    int            registerPattern(const char *pattern, nxt_Dissector *dissector);
    int            registerDone();
    nxt_Dissector* recognizeProto(const uint8_t *payload, uint16_t payloadLen);

private:
    uint32_t pointerToNumber(void *p)
    {
        if (0 == dissectorBaseAddr_)
        {   // 保留高 32bit 到 dissectorBaseAddr_ 中
            this->dissectorBaseAddr_ = reinterpret_cast<uintptr_t>(p) & 0xffffffff00000000;
        }

        // 仅使用低 32bit 作为 number 来标识 dissector;
        return reinterpret_cast<uintptr_t>(p) & 0xffffffff;
    }

    void* pointerFromNumber(uint32_t n)
    {   // 将 n 补充到低 32 bit 恢复为 pointer;
        return reinterpret_cast<void*>(this->dissectorBaseAddr_ | n);
    }

private:
    nxt_recog_type             type_;
    nxt_RegexBlock             *regexBlock_  = NULL;
    nxt_Dissector              *directProto_ = NULL;  // 不需要进行 payload 匹配，直接命中协议;
    uint64_t                    dissectorBaseAddr_ = 0;
};

struct nxt_ProtoRecognizer
{
public:
    int            registerPattern(uint16_t port, const char *pattern, nxt_Dissector *dissector);
    int            registerDone();
    nxt_Dissector* recognizeProto(uint16_t port, uint8_t *payload, uint16_t payloadLen);

private:
    std::vector<std::unique_ptr<nxt_Recognizer>>       tableLevel1_; // 一级表，用于端口 + 特征字场景;
    nxt_Recognizer                                     tableLevel2_; // 二级表，用于不限端口的特定字场景;
};

#endif /* PROTORECOGNIZER_H */
