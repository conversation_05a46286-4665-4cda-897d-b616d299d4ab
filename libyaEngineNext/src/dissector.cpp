#include "dissector.h"
#include "yaEngineNext/nxt_dissector.h"
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/memory.hpp>
#include <glib-2.0/glib.h>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <string.h>
#include <dlfcn.h>

class nxt_HandoffFinal
{
public:
    static int handoffRegister(nxt_handoff_key_t key _U_, nxt_dissector_t *dissector _U_, void *userdata _U_)
    {
        // TODO: 报错, 一个 final dissector 不能被 register handoff;
        return -1;
    }

    static nxt_dissector_t*  handoffFind(nxt_handoff_key_t key _U_, nxt_session_t *session _U_, precord_t *precord _U_, void *userdata _U_)
    {
        // 一个 final dissector 的 find 操作总是返回 NULL;
        return NULL;
    }
};

class nxt_HandoffTable
{
public:
    static int handoffRegister(nxt_handoff_key_t key, nxt_dissector_t *dissector, void *userdata)
    {
        auto &handoffTable = *(std::vector<nxt_Dissector*> *)userdata;;
        if (key.number >= handoffTable.size())
        {
            handoffTable.resize(key.number + 1);
        }

        handoffTable[key.number] = dissector;
        return 0;
    }

    static nxt_dissector_t*  handoffFind(nxt_handoff_key_t key, nxt_session_t *session _U_, precord_t *precord _U_, void *userdata)
    {
        auto &handoffTable = *(std::vector<nxt_Dissector*> *)userdata;;

        // TODO: 考虑在定义 dissector 时支持指定 key.number 的最大值，这样将不用每次查找均进行边界检查;
        if (key.number >= handoffTable.size())
        {
            return NULL;
        }

        return handoffTable[key.number];
    }
};

nxt_Dissector::nxt_Dissector(nxt_dissector_def_t *def, int i)
    : id_(i)
    , protoName_(def->name)
    , type_(def->type)
    , schemaRegFun_(def->schemaRegFun)
    , dissectFun_(def->dissectFun)
    , userdata_(def->userdata)
    , mountAt_(def->mountAt)
{
    switch (def->handoff.type)
    {
    case NXT_HANDOFF_TYPE_NONE:
        handoff_.registerFun       = nxt_HandoffFinal::handoffRegister;
        handoff_.findFun           = nxt_HandoffFinal::handoffFind;
        handoff_.onResolveDoneFunc = NULL;
        handoffUserdata_           = NULL;
        break;

    case NXT_HANDOFF_TYPE_NUMBER:
        handoff_.registerFun       = nxt_HandoffTable::handoffRegister;
        handoff_.findFun           = nxt_HandoffTable::handoffFind;
        handoff_.onResolveDoneFunc = NULL;
        handoffUserdata_           = &handoffTable_;
        break;

    case NXT_HANDOFF_TYPE_PORT_PAYLOAD:
    case NXT_HANDOFF_TYPE_CUSTOM:
        // TODO: 检查 def->handoff 不能为空;
        handoff_.registerFun       = def->handoff.registerFun;
        handoff_.findFun           = def->handoff.findFun;
        handoff_.onResolveDoneFunc = def->handoff.onResolveDoneFunc;
        handoffUserdata_           = def->handoff.userdata;
        break;
    default:
        assert(false);
        break;
    }

    // 是否有 finish 函数?
    char finiFunName[64] = { 0 };
    snprintf(finiFunName, sizeof finiFunName, "nxt_dissector_fini_%s", protoName_);
    this->finiFun_ =  (nxt_cb_dissector_fini)dlsym(NULL, finiFunName);
}

std::unique_ptr<nxt_dissector_t> create_dissector_from_registration(nxt_dissector_def_t *dissectorDef, uint32_t index)
{
    return yv::make_unique<nxt_dissector_t>(dissectorDef, index);
}

class nxt_DissectorKeeper : public nxt::noncopyable
{
public:
    friend int                  nxt_dissector_register(nxt_dissector_def_t *registration);
    friend nxt_dissector_def_t* nxt_dissector_get_first_registration();
    friend uint32_t             nxt_dissector_get_count();
    friend nxt_dissector_t*     nxt_dissector_get_by_index(uint32_t index);

public:
    nxt_DissectorKeeper()
    {
    }

    ~nxt_DissectorKeeper()
    {
    }

public:
    int loadBuiltinDissectors()
    {
        return loadDissectorsFromRegistry();
    }

    int loadDissectorsFromRegistry()
    {
        // do load
        nxt_dissector_def_t *node = dissectorRegistry_;
        for (; node != NULL; node = node->next)
        {
            if (getDissectorByName(node->name))
            {   // 不允许重复注册同一个 proto
                continue;
            }

            auto dissector = create_dissector_from_registration(node, arrayDissector_.size());
            loadDissector(std::move(dissector));
        }

        // 一次 loadDissectorsFromRegistry 之后将 dissector_registry 销毁，
        // 为了后续再有 nxt_plugin_dissector_load 操作时将会新建若干 dissector_registration
        // 载入到 dissectorKeeper 时将只需要处理这一批新的，resolveHandoff 也是这样;
        dissectorRegistry_ = NULL;

        return 0;
    }

    int unloadDissectors()
    {
        // call dissector 的 finiFun
        for (auto *dissector : arrayDissector_)
        {
            if (dissector->finiFun_)
            {
                dissector->finiFun_();
            }
        }

        return 0;
    }

    int appendHandoff(const char *name, nxt_handoff_mnt_t mnt)
    {
        if (NULL == name) {
            return -1;
        }

        mapReverseHandoff_.insert(std::make_pair(std::string(name), mnt));
        return 0;
    }

    int resolveHandoff()
    {
        for (auto & dissector : arrayDissector_)
        {
            nxt_handoff_mnt_t *item = dissector->mountAt_;
            while (!NXT_MNT_IS_END(item) && item->proto)
            {
                nxt_dissector_t *parentDissector = this->getDissectorByName(item->proto);
                if (NULL == parentDissector)
                {
                    // TODO: 应该通过 last error 进行报错
                    LOG_INFO("error: load dissector [%s], but its parent [%s] not loaded yet.\n", dissector->protoName_, item->proto);
                    break;
                }

                parentDissector->registerNextDissector(item->key, dissector);
                item++;
            }
        }

        for (auto & handoff : mapReverseHandoff_) {
            auto dissector = this->getDissectorByName(handoff.first.c_str());
            if (NULL == dissector) {
                LOG_INFO("error: dissector [%s] not found.\n", handoff.first.c_str());
                continue;
            }

            nxt_dissector_t *parentDissector = this->getDissectorByName(handoff.second.proto);
            if (parentDissector == nullptr) {
                LOG_INFO("error: load dissector [%s], but its parent [%s] not loaded yet.\n", dissector->protoName_, handoff.second.proto);
                break;
            }

            parentDissector->registerNextDissector(handoff.second.key, dissector);
        }


        // call on_resolve_done;
        for (auto & dissector : arrayDissector_)
        {
            if (dissector->handoff_.onResolveDoneFunc)
            {
                dissector->handoff_.onResolveDoneFunc(dissector->handoff_.userdata);
            }
        }

        return 0;
    }

    nxt_dissector_t* getDissectorByName(const char *dissectorName)
    {
        auto findIter = mapName2Dissector_.find(dissectorName);
        if (findIter == mapName2Dissector_.end())
        {
            return nullptr;
        }

        return findIter->second.get();
    }

    nxt_dissector_t* getDissectorById(uint32_t dissectorId)
    {
        return arrayDissector_[dissectorId];
    }

    bool dissectorIsTypeOf(const char *name, nxt_dissector_type_enum type)
    {
        if (type == NXT_DISSECTOR_TYPE_TRAILER)
        {
            return std::find(trailerArray_.begin(), trailerArray_.end(), name) != trailerArray_.end();
        }

        return false;
    }

private:
    int loadDissector(std::unique_ptr<nxt_dissector_t> dissector)
    {
        switch (dissector->type_)
        {
        case NXT_DISSECTOR_TYPE_TRAILER:
        {
            trailerArray_.push_back(dissector->protoName_);
            break;
        }

        default:
            break;
        }

        // 不允许重复注册
        if (getDissectorByName(dissector->protoName_) != nullptr)
        {
            return 0;
        }

        arrayDissector_.push_back(dissector.get());
        mapName2Dissector_.emplace(dissector->protoName_, std::move(dissector));
        return 0;
    }

public:
    using nxt_dissector_ptr = std::unique_ptr<nxt_dissector_t>;

    void clear()
    {
        arrayDissector_.clear();
        arrayDissector_.shrink_to_fit();

        linkArray_.clear();
        linkArray_.shrink_to_fit();

        trailerArray_.clear();
        trailerArray_.shrink_to_fit();

        mapName2Dissector_.clear();
    }

private:
    int                                       dissectorCnt_      = 0;
    nxt_dissector_def_t                      *dissectorRegistry_ = nullptr;
    std::vector<nxt_dissector_t *>            arrayDissector_;
    std::vector<std::string>                  linkArray_;
    std::vector<std::string>                  trailerArray_;     // TODO: 似乎没有必要使用 vector 存储(因为有 dissector_type)，除非我们需要输出所有的 trailer?
    std::map<std::string, nxt_dissector_ptr>  mapName2Dissector_;
    // nex_handoff_new_rule 为 dissector 新增的 handoff 规则
    // 结构 {"childProto" : ("parentProto", key)}
    // eg: {"ipv4": ("vlan", 0x0800)}  - 当 vlan 协议遇到规则 0x0800 时，下层解析器为 ipv4
    //     {"ipv4": ("gtp_u", 0xff)}   - 当  gtp_u 协议遇到规则 0xff 时，下层协议为 ipv4
    // 此配置在 dissector 加载完毕之后，自动注册所有 handoff 关系
    std::multimap<std::string, nxt_handoff_mnt_t> mapReverseHandoff_;
};

// 实现 static 效果，未命名 namespace 内变量只有当前文件可以访问
static nxt_DissectorKeeper gDissectorKeeper;

void nxt_Dissector::onSchemaDefRegisterDone(pschema_t* schema)
{
    this->schema_ = schema;
}

int nxt_Dissector::registerSchemaDef(nxt_engine_t *engine, pschema_db_t *db)
{
    if (NULL == this->schemaRegFun_)
    {
        return -1;
    }

    // TODO: init_func 中需要完成协议字段注册，其中必须包括 nxt_dissector_t 的 next_field(是否注册), 需要进行检查,否则该插件加载失败，
    // TODO: 进行检测，schema 是否查询到;
    int res = this->schemaRegFun_(engine, db);

    pschema_t *newSchema = pschema_get_proto(db, this->protoName_);
    this->onSchemaDefRegisterDone(newSchema);
    return res;
}

int nxt_Dissector::doDissect(nxt_engine_t* engine, nxt_session_t* session, nxt_mbuf_t* mbuf)
{
    return this->dissectFun_(engine, session, mbuf);
}

int nxt_dissector_register(nxt_dissector_def_t *registration)
{
    registration->next                  = gDissectorKeeper.dissectorRegistry_;
    gDissectorKeeper.dissectorRegistry_ = registration;

    gDissectorKeeper.dissectorCnt_++;
    return 0;
}

int nxt_dissector_unregister(nxt_dissector_def_t *registration _U_)
{
    return 0;
}

nxt_dissector_def_t* nxt_dissector_get_first_registration()
{
    return gDissectorKeeper.dissectorRegistry_;
}

int nxt_dissector_is_type_of(const char *dissectorName, nxt_dissector_type_enum type)
{
    return gDissectorKeeper.dissectorIsTypeOf(dissectorName, type);
}

nxt_dissector_def_t* nxt_get_next_dissector_registration(nxt_dissector_def_t *registration)
{
    if (NULL == registration)
    {
        return NULL;
    }

    return registration->next;
}

int nxt_dissector_init_all()
{
    // TODO: 该函数以后需要使用脚本生成到 Dissector_init.c 中，调用所有的 nxt_dissector_init_xxx 函数
    return 0;
}

int nxt_dissector_finish_all()
{
    gDissectorKeeper.unloadDissectors();
    gDissectorKeeper.clear();
    return 0;
}

int nxt_dissector_load_builtin()
{
    gDissectorKeeper.loadBuiltinDissectors();

    return 0;
}

nxt_dissector_t *nxt_dissector_get_by_name(const char *name)
{
    return gDissectorKeeper.getDissectorByName(name);
}

uint32_t nxt_dissector_get_count()
{
    return gDissectorKeeper.dissectorCnt_;
}

nxt_dissector_t *nxt_dissector_get_by_index(uint32_t index)
{
    if (index >= gDissectorKeeper.arrayDissector_.size())
    {
        return NULL;
    }

    return gDissectorKeeper.arrayDissector_[index];
}

int nxt_dissector_load_dissectors_from_registry()
{
    return gDissectorKeeper.loadDissectorsFromRegistry();
}

#define PLUGIN_PREFIX "yaNxtDissector_"

class nxt_PluginKeeper
{
public:
    int loadPlugin(const char *pluginPath, char errorBuff[], int buffLen)
    {
        if (!this->isValidPluginName(pluginPath))
        {
            snprintf(errorBuff, buffLen, "bad plugin:%s, plugin name must like:%sxxx.so", pluginPath, PLUGIN_PREFIX);
            return -1;
        }

        void *pluginHandle = dlopen(pluginPath, RTLD_LAZY);
        if (NULL == pluginHandle)
        {
            strncpy(errorBuff, dlerror(), buffLen);
            return -1;
        }

        // call nxt_dissector_init fun
        auto initFun = (nxt_cb_dissector_init)dlsym(pluginHandle, "nxt_dissector_init");
        if (NULL == initFun)
        {
            snprintf(errorBuff, buffLen, "bad plugin:%s, init function nxt_dissector_init not found.", pluginPath);
            return -2;
        }

        initFun();

        arrayPluginHandle_.push_back(pluginHandle);
        return 0;
    }

    int unloadPlugins()
    {
        for (auto & h : arrayPluginHandle_)
        {
            dlclose(h);
        }

        return 0;
    }

    bool isValidPluginName(const char *pluginPath)
    {
        const char *pluginBasename = basename(pluginPath);
        return g_str_has_prefix(pluginBasename, PLUGIN_PREFIX)
            && g_str_has_suffix(pluginBasename, ".so");
    }

    void clear()
    {
        arrayPluginHandle_.clear();
        arrayPluginHandle_.shrink_to_fit();
    }

private:
    std::vector<void *> arrayPluginHandle_;
};

static nxt_PluginKeeper gPluginKeeper;

int nxt_plugin_dissector_load(const char *pluginPath, char errorBuff[], int buffLen)
{
    if (gPluginKeeper.loadPlugin(pluginPath, errorBuff, buffLen) < 0)
    {
        return -1;
    }

    nxt_dissector_load_dissectors_from_registry();
    return 0;
}

int nxt_plugin_dissector_unload_all()
{
    gPluginKeeper.unloadPlugins();
    gPluginKeeper.clear();
    return 0;
}

int nxt_dissector_resolve_handoff()
{
    return gDissectorKeeper.resolveHandoff();
}

nxt_dissector_t* nxt_dissector_get_by_id(int id)
{
    return gDissectorKeeper.getDissectorById(id);
}

int nxt_dissector_do_dissect(nxt_engine_t *engine, nxt_dissector_t* dissector, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
    return dissector->doDissect(engine, session, mbuf);
}

int nxt_handoff_new_rule(int type, const char * parentDissectorName, ...)
{
    va_list args;
    va_start(args, parentDissectorName);

    nxt_handoff_mnt_t mnt;
    mnt.proto = parentDissectorName;

    switch (type) {
    case NXT_HANDOFF_TYPE_NUMBER:
        mnt.key.type = NXT_HANDOFF_TYPE_NUMBER;
        mnt.key.number = va_arg(args, int);
        break;
    case NXT_HANDOFF_TYPE_PORT_PAYLOAD:
        mnt.key.type = NXT_HANDOFF_TYPE_PORT_PAYLOAD;
        mnt.key.portPayload.serverPort = va_arg(args, int);
        mnt.key.portPayload.payloadData = (uint8_t *)va_arg(args, char *);
        break;
    default:
        va_end(args);
        return -1;
    }

    char *dissectorName = va_arg(args, char *);
    if (!dissectorName) {
        va_end(args);
        return -1;
    }

    gDissectorKeeper.appendHandoff(dissectorName, mnt);
    va_end(args);

    return 0;
}