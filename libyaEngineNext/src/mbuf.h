#ifndef MEMORY_BUFF_H
#define MEMORY_BUFF_H

#include "util/ntoh.hpp"
#include "yaEngineNext/nxt_mbuf.h"
#include "exception.hpp"
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/allocator.hpp>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <assert.h>
#include <new>
#include <memory>

// 在检测到异常时是否使用断言进行报告
#define NXT_MBUF_DO_ASSERT 0

enum nxt_mbuf_flag_enum
{
    NXT_MBUF_FLAG_NONE           = 0,
    NXT_MBUF_FLAG_MORE_FRAGMENTS = 1,
    NXT_MBUF_FLAG_DIR_S2C        = 1 << 1,
    NXT_MBUF_FLAG_OWNING_BUFF    = 1 << 2,
};

// mbuf: memory buffer
// mbuf 是一段 memory，是装载着 (packet 或者 string) 的 buffer 容器;
// 1. buffer 大小为 buff_size, buffer 可以读写，可读写的范围由 range_begin 与 range_end 标识;
// 2. mbuf_t 在各个协议层处理器之间传递时会让该层协议仅看到它该处理的部分,
//    例如，框架在将 packet 交给 tcp dissector 之前会将 mbuf range 调整为
//    tcp layer(begin 为 tcp头首字节，end 为 tcp 负载最后一个字节 + 1);
struct nxt_Mbuf
{
public: // create
    nxt_Mbuf()
    {
    }

    // 创建大小为 buff_size 的 buffer, 拥有该 buffer
    nxt_Mbuf(ya_allocator_t *alloc, uint32_t buffSize)
        : buffSize_(buffSize)
        , rangeEnd_(buffSize)
    {
        this->buff_ = (uint8_t *)yv::create_object<void>(alloc, buffSize);
        this->sp_   = std::shared_ptr<uint8_t>(this->buff_, [=](uint8_t *p){ yv::destroy_object<void>(alloc, p); });

        // 当分配内存时，首字节设置为 '\0',
        // 用于在没有进行任何 write 操作进度可以通过 get_string 获得正确的空字符串
        this->buff_[0] = '\0';
    }

    // refer-to buff, 并不拥有该 buffer
    nxt_Mbuf(uint8_t *buff, uint32_t buffSize, uint16_t begin, uint16_t end)
        : buff_(buff)
        , buffSize_(buffSize)
        , rangeBegin_(begin)
        , rangeEnd_(end)

    {
    }

    ~nxt_Mbuf() = default;

    nxt_Mbuf(const nxt_Mbuf&) = default;

public: // write
    int strcpyIn(int offset, const char *str)
    {
        assertCanAccess(0, offset);

        int i = 0;
        for (i = offset;
             *str && this->pos(i) < this->rangeEnd_;
             str++, i++)
        {
            *this->addr(i) = *str;
        }

        // TODO: 需要检查，可能因为 buff 写满而结束循环, 以下操作可能越界;
        *this->addr(i) = *str; // 填充 NULL;
        return i - offset;
    }

    int memcpyIn(int offset, const uint8_t *bytes, uint32_t len)
    {
        assertCanAccess(len, offset);

        memcpy(this->addr(offset), bytes, len);
        return len;
    }

public: // cursor operator
    nxt_mbuf_range_t rangeTell()
    {
        return {rangeBegin_, rangeEnd_};
    }

    nxt_mbuf_range_t rangeSet(int beginNew, int endNew)
    {
        doAssert(0 <= beginNew && beginNew <= endNew);
        doAssert(endNew <= this->buffSize_);

        nxt_mbuf_range_t oldRange = {rangeBegin_, rangeEnd_};
        this->rangeBegin_ = beginNew;
        this->rangeEnd_   = endNew;

        return oldRange;
    }

    nxt_mbuf_range_t rangeAdjust(int beginDiff, int endDiff)
    {
        return this->rangeSet(this->rangeBegin_ + beginDiff, this->rangeEnd_ + endDiff);
    }

public: // get
    uint32_t getLength(int offset = 0)
    {
        return this->rangeEnd_ - this->rangeBegin_  - offset;
    }

    int getCapacity()
    {
        return this->buffSize_;
    }

    const uint8_t* getRaw(uint32_t offset)
    {
        assertCanAccess(0, offset);

        return this->addr(offset);
    }

    uint8_t* getRawUnsafe(uint32_t offset)
    {
        assertCanAccess(0, offset);

        return this->addrUnsafe(offset);
    }

    const uint8_t* getBytes(uint32_t offset, uint32_t len)
    {
        // 确保在 offset 之后依然有 len 长的数据可用;
        assertCanAccess(len, offset);

        return nxt_mbuf_get_raw(this, offset);
    }

public: // get
    template<typename T>
    T getInteger(uint32_t offset)
    {
        assertCanAccess(sizeof(T), offset);

        const T *value  = reinterpret_cast<const T *>(this->addr(offset));
        return *value;
    }

    template<typename T> // NOLINTNEXTLINE(readability-identifier-naming)
    T getInteger_ntoh(uint32_t offset)
    {
        T value = getInteger<T>(offset);

        return nxt_util_ntoh<sizeof(T), T>(value);
    }

    template<typename T>
    T getIntegerAsSizeOfN(uint32_t offset, uint8_t len)
    {
        if (len > sizeof (T))
        {
            return 0;
        }

        assertCanAccess(len, offset);

        // 高地址处，多读取了 (sizeof (T) - len) 字节
        // 在小端序机器上，这多读取的高地址位于整型数值的高位, 高位补0数值大小不会改变;
        // 所以，需要将数值的高位抹为 0;
        const T *value  = reinterpret_cast<const T *>(this->addr(offset));
        T mask = ~(T)0 >> ((sizeof (T) - len) * 8); // 生成高 n bit 为 0，其它全 0xff 的掩码;
        return *value & mask;
    }

    template<typename T> // NOLINTNEXTLINE(readability-identifier-naming)
    T getIntegerAsSizeOfN_ntoh(uint32_t offset, uint8_t len)
    {
        if (len > sizeof (T))
        {
            return 0;
        }

        assertCanAccess(len, offset);

        // 读取 sizeof (T) 字节，多读了若干字节，对于大端序来说，
        // 多读的部分位于高地址即低位，低位增加了，相当于数值进行了“左移”，整体权重提升;
        // 需要先进行端序转换，再进行右移操作进行还原;
        // 例如: 假定有4字节 bytes: aa:bb:cc:dd, 以网络序来读取前3字节，结果应该是 0xaabbcc
        // 1) 读取4字节，解释为 uint32, 主机序，得到 0xddccbbaa;
        // 2) 将多读取的部分置0, 0xddccbbaa & 0x00ffffff -> 0x00ccbbaa;
        // 3) 这里不能先进行“右移”，因为这样会“丢失数位”， 需要先进行端序转换: nhohl(0x00ccbbaa) -> 0xaabbcc00;
        // 4) 将多读取的内容“移除”，“右移”，得到 0xaabbcc;
        T value = getIntegerAsSizeOfN<T>(offset, len);                            // 读取 sizeof (T) 字节，并且将多读取的高位置 0;
        return nxt_util_ntoh<sizeof(T)>(value) >> ((sizeof (T) - len) * 8); // 端序转换，再“右移”，还原大小;
    }

private:
    uint16_t pos(int offset)
    {
        return rangeBegin_ + offset;
    }

    uint8_t* addrUnsafe(int offset)
    {
        return &this->buff_[pos(offset)];
    }

    uint8_t* addr(int offset)
    {
        return &this->buff_[pos(offset)];
    }

private:
    void doAssert(bool condition, nxt_exception_enum exceptEnum = NXT_EXCEPT_MBUF_ACCESS_OUT_OF_RANGE)
    {
        if (!condition)
        {
            nxt_exception_throw(exceptEnum, "mbuf out of range.");
        }
    }

    // TODO: 无符号类型，当 offset 为负数时，可能通过 assert 但发生访问越界；
    void assertCanAccess(uint32_t len, int offset)
    {
        doAssert(offset >= 0 && this->pos(offset + len) <= this->rangeEnd_);
    }

private:
    uint8_t                    *buff_       = 0;
    uint16_t                    buffSize_   = 0; // buff 的大小
    uint16_t                    rangeBegin_ = 0;
    uint16_t                    rangeEnd_   = 0;

#if 0
    uint16_t                    type_       = 0; // 保留字段，暂时不使用, 可能包括 'ether', 'ip', 'non-ip' 等
    uint16_t                    flags_      = 0; // 指示C2S, 是否有分片、是否拥有 data 内存、是否可写等;
    uint16_t                    refCount_   = 0;
    uint16_t                    segCnt_     = 0;
    uint64_t                    timestamp_  = 0;
    struct nxt_Mbuf            *next_       = 0;
#endif

    std::shared_ptr<uint8_t>    sp_;
};

#endif /* MEMORY_BUFF_H */
