#ifndef SESSION_H
#define SESSION_H

#include "yaEngineNext/nxt_engine.h"
#include "engine_widget.hpp"
#include "tcp_flow.h"
#include "util/timer.h"
#include "util/creator.hpp"
#include "util/noncopyable.h"
#include <yaBasicUtils/memory.hpp>
#include <boost/unordered/unordered_flat_map.hpp>

#include "yaEngineNext/test/nxt_isession_proxy.h"

// session 是否使用 arena 类型的 allocator?  TODO: 考虑可能需要有一个 config.h 文件;
#define NXT_SESSION_USE_ARENA_ALLOC

#ifdef NXT_SESSION_USE_ARENA_ALLOC
    #define NXT_SESSION_ALLOC_TYPE YA_ALLOC_ARENA
#else
    #define NXT_SESSION_ALLOC_TYPE YA_ALLOC_DIRECT
#endif

class nxt_SessionKeeper;

typedef struct nxt_Session : public nxt::noncopyable
{
public:
    friend class nxt_SessionKeeper;

public:
    nxt_Session(nxt_SessionKeeper *keeper, uint64_t index, nxt_tuple_5_ipv4_t *t5);
    virtual ~nxt_Session();

public: // enqueue packet and read bytes
            int processPacket(nxt_engine_t *engine, nxt_direction_enum direction, nxt_mbuf_t *mbuf);
            int getAvailBytesLen(nxt_direction_enum direction);
            int read(nxt_direction_enum direction, uint8_t *buff, int buffLen, nxt_stream_read_res_t *readStatus);
    virtual int readToRingbuf(nxt_direction_enum direction, nxt_ringbuf_t *rbuf, uint32_t readLen, nxt_stream_read_res_t *readStatus);

public: // allocator
    using alloc_type = yv::allocator<NXT_SESSION_ALLOC_TYPE>;

    const alloc_type& getAllocator() const
    {
        return alloc_;
    }

public: // event
    virtual int onEvent( nxt_event event _U_, nxt_mbuf_t *mbuf _U_, precord_t *precord _U_)
    {
        return 0;
    }

public: // session status
    void setSessionStatus(nxt_session_state_enum status)
    {
        sessionState_ = status;
    }

    nxt_session_state_enum getSessionStatus()
    {
        return sessionState_;
    }

public: // userdata
    void* allocUserdata(uint32_t sessionZoneSize);
    void* rellocUserdata(uint32_t newSessionZoneSize); // TODO: 该接口不实用，需要移除
    void* getUserdata();

public: // resource management
    void *alloc(size_t size);
    void  defer(nxt_defer_callback_t callback, void *userdata);

public: // 统计信息
    uint64_t getSerialNumber()
    {
        return index_;
    }

    nxt_SessionKeeper* getSessionKeeper()
    {
        return keeper_;
    }

    nxt_dissector_t* getRecognizedProto()
    {
        return proto_;
    }

    void updateSessoinLiveDuration();

    void showStats();

public: // etc
    friend void call_userdata_finish(nxt_Session *session);
    void        onProtoRecognized(nxt_dissector_t *proto);
    nxt_Engine* getEngine();

    const char *getSessionRepr()
    {
#ifndef NDEBUG
        return strRepr_.c_str();
#else
        return "";
#endif
    }

private:
    void setupUserdata(nxt_dissector_t *proto);

private: // 会话基本信息: 五元组，统计信息，协议层级列表
    nxt_session_state_enum  sessionState_;
    nxt_SessionKeeper      *keeper_;
    nxt_dissector_t        *proto_; // session 主协议，通常是应用层协议;
    uint64_t                index_;
    nxt_tuple_5_ipv4_t      t5Ipv4_;
    time_t                  createTime_;
    time_t                  liveDuration_;

private:
    alloc_type alloc_;

private: // flow
    nxt_TcpFlow flowC2S_;
    nxt_TcpFlow flowS2C_;

private: // timer
    nxt_timer_t *timerDestroy_;
    uint64_t     timerDelayCounter_ = 0;
    nxt_timer_t *timerAging_;

private: // sessionZone
    void                          *userdata_      = NULL;
    nxt_cb_session_userdata_init   userdataInitFun_;
    nxt_cb_session_userdata_finish userdataFinishFun_;

private: // resource
    nxt_ResourceLifetime lifetime_;

private: // etc
#ifndef NDEBUG
    std::string        strRepr_;
#endif

} nxt_session_t;

class nxt_PsudoSession : public nxt_Session
{
public:
    nxt_PsudoSession(nxt_SessionKeeper *keeper, uint64_t index, nxt_tuple_5_ipv4_t *t5, nxt_ISessionProxy *proxy);
public:
    int readToRingbuf(nxt_direction_enum direction, nxt_ringbuf_t *rbuf, uint32_t readLen, nxt_stream_read_res_t *readStatus) {
        return proxy_->readToRingbuf(direction, rbuf, readLen, readStatus);
    }

    int onEvent( nxt_event event, nxt_mbuf_t *mbuf, precord_t *precord) {
        return proxy_->onEvent(event, mbuf, precord);
    }

private:
    nxt_ISessionProxy *proxy_;
};

inline
bool operator ==(const nxt_tuple_5_ipv4_t& left, const nxt_tuple_5_ipv4_t& right)
{
    if (left.proto != right.proto)
    {
        return false;
    }

    if (left.srcAddr == right.srcAddr
        && left.dstAddr == right.dstAddr
        && left.srcPort == right.srcPort
        && left.dstPort == right.dstPort)
    {
        return true;
    }

    if (left.srcAddr == right.dstAddr
        && left.dstAddr == right.srcAddr
        && left.srcPort == right.dstPort
        && left.dstPort == right.srcPort)
    {
        return true;
    }

    return false;
}

struct nxt_Tuple5Ipv4Hash
{
    size_t operator()(const nxt_tuple_5_ipv4_t& f) const
    {
        return std::hash<uint8_t>()(f.proto)
            ^ std::hash<uint32_t>()(f.srcAddr) ^ std::hash<uint32_t>()(f.dstAddr)
            ^ std::hash<uint16_t>()(f.srcPort) ^ std::hash<uint16_t>()(f.dstPort);
    }
};

class nxt_SessionKeeper : public nxt::noncopyable
{
public:
    friend nxt_Session;

public:
    nxt_SessionKeeper(nxt_Engine *engine, ya_allocator_t *alloc, nxt_timer_scheduler_t *timerScheduler)
        : engine_(engine)
        , alloc_(alloc)
        , timerScheduler_(timerScheduler)
    {
    }

    ~nxt_SessionKeeper();

    nxt_session_t*       findSession(nxt_tuple_5_ipv4_t *t5);
    nxt_session_t*       newTcpSession(nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf, nxt_dissector_t *tcpDissector, precord_t *precord);
    nxt_session_t*       newUdpSession(nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf, nxt_dissector_t *udpDissector, precord_t *precord);
    int                  destroySession(nxt_tuple_5_ipv4_t *t5);
    int                  destroySession(nxt_Session *session);

    nxt_Engine*          getEngine()
    {
        return engine_;
    }

    ya_allocator_t* getAllocator()
    {
        return alloc_;
    }

    nxt_timer_scheduler_t* getTimerScheduler()
    {
        return timerScheduler_;
    }

public: // 统计信息
    uint64_t getSessionCount()
    {
        return ipv4SessionTable_.size();
    }

private:
    nxt_session_t* newSession(nxt_tuple_5_ipv4_t *t5);

private:
    nxt_Engine            *engine_;
    ya_allocator_t        *alloc_;
    nxt_timer_scheduler_t *timerScheduler_;

private:
    // map;
    uint64_t nextSessionNumber_ = 0;
    boost::unordered_flat_map<nxt_tuple_5_ipv4_t, nxt_Session*, nxt_Tuple5Ipv4Hash> ipv4SessionTable_;
};

nxt_tuple_5_ipv4_t nxt_tuple_5_ipv4_reverse(nxt_tuple_5_ipv4_t *t5From);

#endif /* SESSION_H */
