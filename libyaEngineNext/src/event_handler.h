#ifndef EVENTHANDLER_H
#define EVENTHANDLER_H

#include "yaEngineNext/nxt_engine.h"
#include <vector>

typedef struct nxt_EventHandler
{
    nxt_cb_event_handler fun;
    void *userdata;
} nxt_event_handler_t;

struct nxt_EventDispatcher
{
    nxt_event_enum event;
    std::vector<nxt_EventHandler> handlers;

public:
    int addEventHandler(nxt_cb_event_handler handler, void *userdata)
    {
        handlers.push_back({handler, userdata});
        return 0;
    }

    int fireEventHandlers(nxt_engine_t *engine, nxt_pmessage_t *protoMessage)
    {
        for (auto &handler : handlers)
        {
            handler.fun(engine, protoMessage, handler.userdata);
        }

        return 0;
    }
};

class nxt_EventDispatcherKeeper
{
public:
    int addEventHandler(nxt_event_enum event, nxt_cb_event_handler handler, void *userdata)
    {
        return dispatchArray_[event].addEventHandler(handler, userdata);
    }

    int fireEvent(nxt_event_enum event, nxt_engine_t *engine, nxt_pmessage_t *pmessage)
    {
        return dispatchArray_[event].fireEventHandlers(engine, pmessage);
    }

private:
    nxt_EventDispatcher dispatchArray_[NXT_EVENT_MAX];
};

#endif /* EVENTHANDLER_H */
