# cmake version
cmake_minimum_required(VERSION 3.14)

#
# enable testing
#
# 配置 memcheck 不报告 'still reachable', 默认会误报 glib 存在泄漏
set(MEMORYCHECK_COMMAND_OPTIONS "-q --tool=memcheck --leak-check=full --num-callers=50 --trace-children=yes")
include(CTest)
enable_testing()

# generate compile_commands.json
set(CMAKE_EXPORT_COMPILE_COMMANDS on)

# variables
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)

#
# tests
#
add_subdirectory(yaLiteDpi)
add_subdirectory(yaStreamChecksum)

#
# test basic dissect via yaLiteDpi
#
add_test(NAME test_basic_ip_dissect
  COMMAND $<TARGET_FILE:yaLiteDpi> -r ${CMAKE_CURRENT_SOURCE_DIR}/pcaps/proto-http-gitlab.pcap
)

add_test(NAME test_udp_rtp
  COMMAND $<TARGET_FILE:yaLiteDpi> -r ${CMAKE_CURRENT_SOURCE_DIR}/pcaps/proto-rtp-h264.pcap
)

#
# test_story_checksum: 测试基本 tcp 协议的解析;
#
add_test(NAME test_story_checksum
  COMMAND $<TARGET_FILE:yaStreamChecksum> ${CMAKE_CURRENT_SOURCE_DIR}/pcaps/proto-story-alice.pcap
)

set_tests_properties(test_story_checksum PROPERTIES
  PASS_REGULAR_EXPRESSION "4ee77d1236c6b8b6e23f26312a78487c" # 输出内容中必须含有该 md5 串
)

#
# test_story_checksum: 测试基本 tcp 协议的解析;
#
add_test(NAME test_shuffle_story_checksum
  COMMAND $<TARGET_FILE:yaStreamChecksum> ${CMAKE_CURRENT_SOURCE_DIR}/pcaps/proto-story-alice-shuffle.pcap
)

set_tests_properties(test_shuffle_story_checksum PROPERTIES
  PASS_REGULAR_EXPRESSION "4ee77d1236c6b8b6e23f26312a78487c" # 输出内容中必须含有该 md5 串
)
