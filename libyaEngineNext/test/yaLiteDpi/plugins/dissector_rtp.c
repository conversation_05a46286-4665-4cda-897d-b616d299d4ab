#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME     "rtp"
#define HEADER_LEN_RTP 12

static
int rtp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    precord_put(precord, "p_type",    uinteger, nxt_mbuf_get_uint8(mbuf, 1));
    precord_put(precord, "seq",       uinteger, nxt_mbuf_get_uint16_ntoh(mbuf, 2));
    precord_put(precord, "timestamp", uinteger, nxt_mbuf_get_uint32_ntoh(mbuf, 4));
    precord_put(precord, "ssrc",      bytes,    nxt_mbuf_get_raw(mbuf, 8), 4);

    return HEADER_LEN_RTP;
}

static
int rtp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "rtp");
     pschema_register_field(pschema, "flag",      YA_FT_UINT8,  "rtp flag");
     pschema_register_field(pschema, "p_type",    YA_FT_UINT8,  "rtp payload type");
     pschema_register_field(pschema, "seq",       YA_FT_UINT16, "rtp seq");
     pschema_register_field(pschema, "timestamp", YA_FT_UINT32, "rtp timestamp");
     pschema_register_field(pschema, "ssrc",      YA_FT_BYTES,  "rtp ssrc");

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rtp",
    .schemaRegFun = rtp_schema_reg,
    .dissectFun   = rtp_dissect,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 8000),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rtp)
{
    nxt_dissector_register(&gDissectorDef);
}
