cmake_minimum_required(VERSION 3.14)

# find_package(yaEngineNext REQUIRED)
include(${CMAKE_SOURCE_DIR}/cmake/yaEngineNextConfig.cmake)

#
# plugins
#
addEngineNextPlugin(udp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_udp.c
)

addEngineNextPlugin(rtp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_rtp.c
)

addEngineNextPlugin(dns ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_dns.c
)

addEngineNextPlugin(rt ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_trailer_rt.c
)

addEngineNextPlugin(sip ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES       dissector_sip.c
  RAGEL_SOURCES parser_sip.rl
)
