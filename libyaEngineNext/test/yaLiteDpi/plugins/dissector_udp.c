#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME     "udp"
#define HEADER_LEN_UDP 8

static
int udp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    uint16_t srcport = nxt_mbuf_get_uint16_ntoh(mbuf, 0);
    uint16_t dstport = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    precord_put(precord, "srcport", uinteger, srcport);
    precord_put(precord, "dstport", uinteger, dstport);

    precord_put_to_layer(precord, "basic", "SrcPort", uinteger, srcport);
    precord_put_to_layer(precord, "basic", "DstPort", uinteger, dstport);

    // handoff
    nxt_handoff_set_key_of_number(engine, srcport < dstport ? srcport : dstport);

    // 当前 packet 方向设定
    // 方向设定需要有更可靠的方法，例如：sync 帧的 src 为 client
    nxt_direction_enum packetDirection = srcport < dstport ? NXT_DIR_S2C : NXT_DIR_C2S;
    nxt_engine_regzone_put_direction(engine, packetDirection);

    return HEADER_LEN_UDP;
}

static
int udp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "udp");
     pschema_register_field(pschema, "srcport",  YA_FT_UINT16,  "src udp port");
     pschema_register_field(pschema, "dstport",  YA_FT_UINT16,  "dst udp port");
     pschema_register_field(pschema, "length",   YA_FT_UINT16,  "udp payload length");

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "udp",
    .schemaRegFun = udp_schema_reg,
    .dissectFun   = udp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT, // TODO: 这里应该可以选择给出一个字段，表明将其作为 handoff key;
    .mountAt      = {
        NXT_MNT_NUMBER("ipv4", 17),      // TODO: 这里也应该可以选择给出一个字段，表明挂载到 ipv4 的某字段上;
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(udp)
{
    nxt_dissector_register(&gDissectorDef);
}
