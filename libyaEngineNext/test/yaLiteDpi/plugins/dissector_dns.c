#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>

#define PROTO_NAME     "dns"

/* type values  */
#define T_A              1              /* host address */
#define T_NS             2              /* authoritative name server */
#define T_MD             3              /* mail destination (obsolete) */
#define T_MF             4              /* mail forwarder (obsolete) */
#define T_CNAME          5              /* canonical name */
#define T_SOA            6              /* start of authority zone */
#define T_MB             7              /* mailbox domain name (experimental) */
#define T_MG             8              /* mail group member (experimental) */
#define T_MR             9              /* mail rename domain name (experimental) */
#define T_NULL          10              /* null RR (experimental) */
#define T_WKS           11              /* well known service */
#define T_PTR           12              /* domain name pointer */
#define T_HINFO         13              /* host information */
#define T_MINFO         14              /* mailbox or mail list information */
#define T_MX            15              /* mail routing information */
#define T_TXT           16              /* text strings */
#define T_RP            17              /* responsible person (RFC 1183) */
#define T_AFSDB         18              /* AFS data base location (RFC 1183) */
#define T_X25           19              /* X.25 address (RFC 1183) */
#define T_ISDN          20              /* ISDN address (RFC 1183) */
#define T_RT            21              /* route-through (RFC 1183) */
#define T_NSAP          22              /* OSI NSAP (RFC 1706) */
#define T_NSAP_PTR      23              /* PTR equivalent for OSI NSAP (RFC 1348 - obsolete) */
#define T_SIG           24              /* digital signature (RFC 2535) */
#define T_KEY           25              /* public key (RFC 2535) */
#define T_PX            26              /* pointer to X.400/RFC822 mapping info (RFC 1664) */
#define T_GPOS          27              /* geographical position (RFC 1712) */
#define T_AAAA          28              /* IPv6 address (RFC 1886) */
#define T_LOC           29              /* geographical location (RFC 1876) */
#define T_NXT           30              /* "next" name (RFC 2535) */
#define T_EID           31              /* Endpoint Identifier */
#define T_NIMLOC        32              /* Nimrod Locator */
#define T_SRV           33              /* service location (RFC 2052) */
#define T_ATMA          34              /* ATM Address */
#define T_NAPTR         35              /* naming authority pointer (RFC 3403) */
#define T_KX            36              /* Key Exchange (RFC 2230) */
#define T_CERT          37              /* Certificate (RFC 4398) */
#define T_A6            38              /* IPv6 address with indirection (RFC 2874 - obsolete) */
#define T_DNAME         39              /* Non-terminal DNS name redirection (RFC 2672) */
#define T_SINK          40              /* SINK */
#define T_OPT           41              /* OPT pseudo-RR (RFC 2671) */
#define T_APL           42              /* Lists of Address Prefixes (APL RR) (RFC 3123) */
#define T_DS            43              /* Delegation Signer (RFC 4034) */
#define T_SSHFP         44              /* Using DNS to Securely Publish SSH Key Fingerprints (RFC 4255) */
#define T_IPSECKEY      45              /* RFC 4025 */
#define T_RRSIG         46              /* RFC 4034 */
#define T_NSEC          47              /* RFC 4034 */
#define T_DNSKEY        48              /* RFC 4034 */
#define T_DHCID         49              /* DHCID RR (RFC 4701) */
#define T_NSEC3         50              /* Next secure hash (RFC 5155) */
#define T_NSEC3PARAM    51              /* NSEC3 parameters (RFC 5155) */
#define T_TLSA          52              /* TLSA (RFC 6698) */
#define T_HIP           55              /* Host Identity Protocol (HIP) RR (RFC 5205) */
#define T_NINFO         56              /* NINFO */
#define T_RKEY          57              /* RKEY */
#define T_TALINK        58              /* Trust Anchor LINK */
#define T_CDS           59              /* Child DS (RFC7344)*/
#define T_CDNSKEY       60              /* DNSKEY(s) the Child wants reflected in DS ( [RFC7344])*/
#define T_OPENPGPKEY    61              /* OPENPGPKEY draft-ietf-dane-openpgpkey-00 */
#define T_CSYNC         62              /* Child To Parent Synchronization (RFC7477) */
#define T_SPF           99              /* SPF RR (RFC 4408) section 3 */
#define T_UINFO        100              /* [IANA-Reserved] */
#define T_UID          101              /* [IANA-Reserved] */
#define T_GID          102              /* [IANA-Reserved] */
#define T_UNSPEC       103              /* [IANA-Reserved] */
#define T_NID          104              /* ILNP [RFC6742] */
#define T_L32          105              /* ILNP [RFC6742] */
#define T_L64          106              /* ILNP [RFC6742] */
#define T_LP           107              /* ILNP [RFC6742] */
#define T_EUI48        108              /* EUI 48 Address (RFC7043) */
#define T_EUI64        109              /* EUI 64 Address (RFC7043) */
#define T_TKEY         249              /* Transaction Key (RFC 2930) */
#define T_TSIG         250              /* Transaction Signature (RFC 2845) */
#define T_IXFR         251              /* incremental transfer (RFC 1995) */
#define T_AXFR         252              /* transfer of an entire zone (RFC 5936) */
#define T_MAILB        253              /* mailbox-related RRs (MB, MG or MR) (RFC 1035) */
#define T_MAILA        254              /* mail agent RRs (OBSOLETE - see MX) (RFC 1035) */
#define T_ANY          255              /* A request for all records (RFC 1035) */
#define T_URI          256              /* URI */
#define T_CAA          257              /* Certification Authority Authorization (RFC 6844) */
#define T_TA         32768              /* DNSSEC Trust Authorities */
#define T_DLV        32769              /* DNSSEC Lookaside Validation (DLV) DNS Resource Record (RFC 4431) */
#define T_WINS       65281              /* Microsoft's WINS RR */
#define T_WINS_R     65282              /* Microsoft's WINS-R RR */
#define T_XPF        65422              /* XPF draft-bellis-dnsop-xpf */

typedef struct nxt_DnsContext
{
    int dnsOriginPos;
} nxt_dns_context_t;

static
int dns_dissect_domain_data_labels(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, int offset, nxt_mbuf_t *mbufDomainName, int *writeOffset)
{
    uint8_t labelLen = 0;

    for (; (labelLen = nxt_mbuf_get_uint8(mbuf, offset++)) != 0; offset += labelLen)
    {  // TODO: 对于 label_len 大于 64 时，应该抛出异常，如果暂不处理 pointer
        if ((labelLen & 0xc0) == 0xc0) // pointer 模式的 dns domain name
        {
            // TODO: 如何在编译时发现错误的使用了 uint8_t ?
            uint16_t pointerOffset = ((labelLen & ~0xc0) << 8) | nxt_mbuf_get_uint8(mbuf, offset++);

            // pointer_offset 是相对 'dns 起始点' 的, mbuf_ref 必须先回到原点
            nxt_mbuf_range_t range = nxt_mbuf_range_tell(mbuf);
            nxt_mbuf_range_set(mbuf, ctx->dnsOriginPos, range.end);
            dns_dissect_domain_data_labels(ctx, mbuf, pointerOffset, mbufDomainName, writeOffset);

            // 恢复 mbuf range;
            nxt_mbuf_range_set(mbuf, range.begin, range.end);
            return offset;
        }

        // TODO: 此处不安全，会带来越界 read, 应该使用“合并” 接口，同时进行 mbuf_append
        const char *label = nxt_mbuf_get_string(mbuf, offset);

        // label 不是以 '\0' 结尾的，此处不能使用 nxt_mbuf_append_string
        *writeOffset += nxt_mbuf_strncpy_in(mbufDomainName, *writeOffset, label, labelLen);
        *writeOffset += nxt_mbuf_strcpy_in(mbufDomainName, *writeOffset, ".");
    }

    return offset;
}

static
int dns_dissect_labels(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, nxt_mbuf_t *mbufDnsName)
{
    int domainBuffWriteOffset = 0;
    int consumeLen = dns_dissect_domain_data_labels(ctx, mbuf, 0, mbufDnsName, &domainBuffWriteOffset);
    if (consumeLen == 1) // 遇到了'\0' 作为一个 label, -> 表示 <Root>
    {
        domainBuffWriteOffset += nxt_mbuf_strcpy_in(mbufDnsName, domainBuffWriteOffset, "<Root>");
    }
    else
    {
        // drop last '.'
        nxt_mbuf_strcpy_in(mbufDnsName, domainBuffWriteOffset - 1, "");
    }

    /* printf("dns_name:(%d) %s\n", consume_len, nxt_mbuf_get_str(mbuf_dns_name, 0)); */
    return consumeLen;
}

static
int dns_dissect_query_record(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, precord_t *precord)
{
    uint8_t buff[200];
    ya_allocator_t *alloc = ya_allocator_get_default();
    nxt_mbuf_t *mbufDomainName = nxt_mbuf_new_by_ref_wa(alloc, buff, sizeof buff);

    int offset = dns_dissect_labels(ctx, mbuf, mbufDomainName);
    precord_put(precord, "query_name",  string,   nxt_mbuf_get_string(mbufDomainName, 0));
    precord_put(precord, "query_type",  uinteger, nxt_mbuf_get_uint16_ntoh(mbuf, offset)); offset += 2;
    precord_put(precord, "query_class", uinteger, nxt_mbuf_get_uint16_ntoh(mbuf, offset)); offset += 2;

    nxt_mbuf_free_wa(alloc, mbufDomainName);
    return offset;
}

static
int dns_dissect_queries(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, int cnt, precord_t *precord)
{
    int offset = 0;

    while (cnt-- > 0)
    {
        offset = dns_dissect_query_record(ctx, mbuf, precord);
    }

    return offset;
}

static
int dns_dissect_rdata(nxt_dns_context_t *ctx, uint16_t type, nxt_mbuf_t *mbuf, uint16_t offset, int len _U_, nxt_mbuf_t* mbufOut)
{
    switch (type)
    {
    case T_A:
    {
        // TODO: ftype 是否需要允许变更一个字段的类型？例如这里的字段可能是 ipv4, 也可能是 string;
        uint32_t ipAddr  = nxt_mbuf_get_uint32_ntoh(mbuf, offset); // TODO: mbuf 是否需要支持直接获取 ipv4, 例如转换为 string? 不，内存从哪里来？
        char     buff[20] = { 0 };
        inet_ntop(AF_INET, &ipAddr, buff, sizeof buff);
        nxt_mbuf_strcpy_in(mbufOut, 0, buff);
        break;
    }
    case T_NS:
    case T_MD:
    case T_MF:
    case T_CNAME:
    {
        // dissect_dns_lables 需要从当前位置开始读取
        nxt_mbuf_range_t range = nxt_mbuf_range_adjust(mbuf, offset, 0); // 将 range 调整到 dns label 处;
        dns_dissect_labels(ctx, mbuf, mbufOut);
        nxt_mbuf_range_set(mbuf, range.begin, range.end);
        break;
    }
    }

    return 0;
}

static
int dns_dissect_rr(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, nxt_mbuf_t *mbufRrName, uint16_t *rrType, uint16_t *rrClass, uint32_t *rrTtl,
                   uint16_t *rrDataLen, nxt_mbuf_t *mbufRrData)
{
    // rr(resource record, 当 type 不为 OPT 时) 由 name, type, class, ttl, data_len, data 构成
    int offset   = dns_dissect_labels(ctx, mbuf, mbufRrName);
    *rrType      = nxt_mbuf_get_uint16_ntoh(mbuf, offset); offset += 2; // offset 指向 type 字段之后;

    if (*rrType == T_OPT)
    {   // TODO: 此处应该抛出异常，OPT 类型暂不支持解析;
        // OPT 类型格式：type(2), udp payload size(2), Higher bits in extended(1), EDNS0 version(1), Z(2), Data Len(2), Data
        offset += 2 + 1 + 1 + 2;
        uint16_t dataLen = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
        offset += dataLen;

        return offset;
    }

    *rrClass    = nxt_mbuf_get_uint16_ntoh(mbuf, offset); offset += 2;
    *rrTtl      = nxt_mbuf_get_uint32_ntoh(mbuf, offset); offset += 4; // TODO: 如果这时错误写为了 get_uint16_ntoh, 如果发现?
    *rrDataLen  = nxt_mbuf_get_uint16_ntoh(mbuf, offset); offset += 2;

    // dissect_dns_rdata, result in mbuf_rr_data as string
    dns_dissect_rdata(ctx, *rrType, mbuf, offset, *rrDataLen, mbufRrData);

    offset += *rrDataLen;

    return offset;
}

static
int dns_dissect_answers(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, int cnt, precord_t *precord)
{
    int offset       = 0;
    char rrName[200] = { 0 };
    char rrData[200] = { 0 };

    // 添加一个 array fvalue
    ya_fvalue_t* fvArray = precord_put(precord, "answer_array", array);
    ya_allocator_t *alloc = ya_allocator_get_default();

    while (cnt-- > 0)
    {
        nxt_mbuf_t *mbufRrName = nxt_mbuf_new_by_ref_wa(alloc, (uint8_t *)rrName, sizeof rrName);
        nxt_mbuf_t *mbufRrData = nxt_mbuf_new_by_ref_wa(alloc, (uint8_t *)rrData, sizeof rrName);
        uint16_t    rrType     = 0;
        uint16_t    rrClass    = 0;
        uint32_t    rrTtl      = 0;
        uint16_t    rrDataLen  = 0;

        rrName[0] = '\0';
        rrData[0] = '\0';

        offset = dns_dissect_rr(ctx, mbuf, mbufRrName, &rrType, &rrClass, &rrTtl, &rrDataLen, mbufRrData);
        nxt_mbuf_range_adjust(mbuf, offset, 0);

        // 向 fv_array 中 append 一个 table 类型的 sub value;
        ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);

        // 向 fv_table 中 put 若干 sub value
        precord_sub_put(precord, fvTable, "name",     string,   YA_FT_STRING, rrName);
        precord_sub_put(precord, fvTable, "type",     uinteger, YA_FT_UINT32, rrType);
        precord_sub_put(precord, fvTable, "class",    uinteger, YA_FT_UINT32, rrClass);
        precord_sub_put(precord, fvTable, "ttl",      uinteger, YA_FT_UINT32, rrTtl);
        precord_sub_put(precord, fvTable, "data_len", uinteger, YA_FT_UINT32, rrDataLen);
        precord_sub_put(precord, fvTable, "data",     string,   YA_FT_STRING, rrData);

        nxt_mbuf_free_wa(alloc, mbufRrName);
        nxt_mbuf_free_wa(alloc, mbufRrData);
    }

    return 0;
}

static
int dns_dissect_authes(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, int cnt, precord_t *precord)
{
    int offset       = 0;
    char rrName[200] = { 0 };
    char rrData[200] = { 0 };

    // 添加一个 array fvalue
    ya_fvalue_t* fvArray = precord_put(precord, "auth_array", array);
    ya_allocator_t *alloc = ya_allocator_get_default();

    while (cnt-- > 0)
    {
        nxt_mbuf_t *mbufRrName = nxt_mbuf_new_by_ref_wa(alloc, (uint8_t *)rrName, sizeof rrName);
        nxt_mbuf_t *mbufRrData = nxt_mbuf_new_by_ref_wa(alloc, (uint8_t *)rrData, sizeof rrName);
        uint16_t    rrType     = 0;
        uint16_t    rrClass    = 0;
        uint32_t    rrTtl      = 0;
        uint16_t    rrDataLen  = 0;

        rrName[0] = '\0'; // 清空可能的残留值;
        rrData[0] = '\0'; // 清空可能的残留值;

        offset = dns_dissect_rr(ctx, mbuf, mbufRrName, &rrType, &rrClass, &rrTtl, &rrDataLen, mbufRrData);
        nxt_mbuf_range_adjust(mbuf, offset, 0);

        // 向 fv_array 中 append 一个 table 类型的 sub value;
        ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);

        // 向 fv_table 中 put 若干 sub value
        precord_sub_put(precord, fvTable, "name",     string,   YA_FT_STRING, rrName);
        precord_sub_put(precord, fvTable, "type",     uinteger, YA_FT_UINT32, rrType);
        precord_sub_put(precord, fvTable, "class",    uinteger, YA_FT_UINT32, rrClass);
        precord_sub_put(precord, fvTable, "ttl",      uinteger, YA_FT_UINT32, rrTtl);
        precord_sub_put(precord, fvTable, "data_len", uinteger, YA_FT_UINT32, rrDataLen);
        precord_sub_put(precord, fvTable, "data",     string,   YA_FT_STRING, rrData);

        nxt_mbuf_free_wa(alloc, mbufRrName);
        nxt_mbuf_free_wa(alloc, mbufRrData);
    }

    return 0;
}

static
int dns_dissect_additional(nxt_dns_context_t *ctx, nxt_mbuf_t *mbuf, int cnt, precord_t *precord)
{
    int offset       = 0;
    char rrName[200] = { 0 };
    char rrData[200] = { 0 };

    // 添加一个 array fvalue
    ya_fvalue_t* fvArray = precord_put(precord, "addi_array", array);
    ya_allocator_t *alloc = ya_allocator_get_default();

    while (cnt-- > 0)
    {
        nxt_mbuf_t *mbufRrName = nxt_mbuf_new_by_ref_wa(alloc, (uint8_t *)rrName, sizeof rrName);
        nxt_mbuf_t *mbufRrData = nxt_mbuf_new_by_ref_wa(alloc, (uint8_t *)rrData, sizeof rrName);
        uint16_t    rrType     = 0;
        uint16_t    rrClass    = 0;
        uint32_t    rrTtl      = 0;
        uint16_t    rrDataLen  = 0;

        rrName[0] = '\0'; // 清空可能的残留值;
        rrData[0] = '\0'; // 清空可能的残留值;

        offset = dns_dissect_rr(ctx, mbuf, mbufRrName, &rrType, &rrClass, &rrTtl, &rrDataLen, mbufRrData);
        nxt_mbuf_range_adjust(mbuf, offset, 0);

        // 向 fv_array 中 append 一个 table 类型的 sub value;
        ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);

        // 向 fv_table 中 put 若干 sub value
        precord_sub_put(precord, fvTable, "name",     string,   YA_FT_STRING, rrName);
        precord_sub_put(precord, fvTable, "type",     uinteger, YA_FT_UINT32, rrType);
        precord_sub_put(precord, fvTable, "class",    uinteger, YA_FT_UINT32, rrClass);
        precord_sub_put(precord, fvTable, "ttl",      uinteger, YA_FT_UINT32, rrTtl);
        precord_sub_put(precord, fvTable, "data_len", uinteger, YA_FT_UINT32, rrDataLen);
        precord_sub_put(precord, fvTable, "data",     string,   YA_FT_STRING, rrData);

        nxt_mbuf_free_wa(alloc, mbufRrName);
        nxt_mbuf_free_wa(alloc, mbufRrData);
    }

    return 0;
}

static
int dns_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
#define DNS_QUERY_CNT_MAX 50

    // 长度至少需要大于 12,
    // transaction_id(2B), flags(2B), questions_cnt(2B),
    // answers_cnt(2B), auth_rr_cnt(2B), addit_rr_cnt(2B)
    if (nxt_mbuf_get_length(mbuf) < 12)
    {
        /* printf("bad dns packet, too short:%d\n", nxt_mbuf_get_length(mbuf)); */
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    int        lret    = nxt_mbuf_get_length(mbuf);
    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    uint32_t offset = 0;
    precord_put(precord, "transaction_id",    uinteger, nxt_mbuf_get_uint16(mbuf, offset)); offset += 2;
    precord_put(precord, "flags",             uinteger, nxt_mbuf_get_uint16(mbuf, offset)); offset += 2;

    uint16_t questionsCnt = nxt_mbuf_get_uint16_ntoh(mbuf, offset); offset += 2;
    uint16_t answersCnt   = nxt_mbuf_get_uint16_ntoh(mbuf, offset); offset += 2;
    uint16_t authRrCnt    = nxt_mbuf_get_uint16_ntoh(mbuf, offset); offset += 2;
    uint16_t additRrCnt   = nxt_mbuf_get_uint16_ntoh(mbuf, offset); offset += 2;

    // 有效性检测
    if (questionsCnt  > DNS_QUERY_CNT_MAX
        || answersCnt > DNS_QUERY_CNT_MAX
        || authRrCnt  > DNS_QUERY_CNT_MAX
        || additRrCnt > DNS_QUERY_CNT_MAX)
    {
        // TODO: 考虑是否需要增加一些额外报错信息，例如通过 set_error 等;
        /* printf("invalid dns packet, cnt error:%d, %d, %d, %d\n", questions_cnt, answers_cnt, auth_rr_cnt, addit_rr_cnt); */
        lret = NXT_DISSECT_ST_VERIFY_FAILED;
        goto OUT;
    }

    precord_put(precord, "questions_cnt",     uinteger, questionsCnt);
    precord_put(precord, "answers_cnt",       uinteger, answersCnt);
    precord_put(precord, "auth_rr_cnt",       uinteger, authRrCnt);
    precord_put(precord, "additional_rr_cnt", uinteger, additRrCnt);

    nxt_dns_context_t ctx      = {0};
    nxt_mbuf_range_t  rangeDns = nxt_mbuf_range_tell(mbuf);
    ctx.dnsOriginPos           = rangeDns.begin;

    // 调整 range 到 query 处
    nxt_mbuf_range_adjust(mbuf, offset, 0);

    if (questionsCnt > 0)
    {
        offset = dns_dissect_queries(&ctx, mbuf, questionsCnt, precord);
        nxt_mbuf_range_adjust(mbuf, offset, 0);
    }

    if (answersCnt > 0)
    {
        offset = dns_dissect_answers(&ctx, mbuf, answersCnt, precord);
        nxt_mbuf_range_adjust(mbuf, offset, 0);
    }

    if (authRrCnt > 0)
    {
        offset = dns_dissect_authes(&ctx, mbuf, authRrCnt, precord);
        nxt_mbuf_range_adjust(mbuf, offset, 0);
    }

    if (additRrCnt > 0)
    {
        offset = dns_dissect_additional(&ctx, mbuf, additRrCnt, precord);
        nxt_mbuf_range_adjust(mbuf, offset, 0);
    }

    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

OUT:
    nxt_session_destroy_record(engine, session, precord);
    return lret;
}

static
int dns_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "dns");

     pschema_register_field_ex(pschema, "transaction_id",   YA_FT_UINT16,  "transaction id", YA_DISPLAY_BASE_HEX);
     pschema_register_field(pschema,    "flags",            YA_FT_UINT16,  "flags");
     pschema_register_field(pschema,    "questions_cnt",    YA_FT_UINT16,  "questions count");
     pschema_register_field(pschema,    "answers_cnt",      YA_FT_UINT16,  "answers count");
     pschema_register_field(pschema,    "auth_rr_cnt",      YA_FT_UINT16,  "auth RR count");
     pschema_register_field(pschema,    "additional_rr_cnt",YA_FT_UINT16,  "additional RR count");

     // query 由 name, type, class 构成
     pschema_register_field(pschema, "query_name",  YA_FT_STRING, "query name");
     pschema_register_field(pschema, "query_type",  YA_FT_UINT16, "query type");
     pschema_register_field(pschema, "query_class", YA_FT_UINT16, "query class");

     // answer RRs, answer_array schema: [{k:v | k in [name, type, class, ttl, data_len, data]}]
     pschema_register_field(pschema, "answer_array",     YA_FT_ARRAY, "answer array");

     // authority RRs, schema: [{k:v | k in [name, type, class, ttl, data_len, data]}]
     pschema_register_field(pschema, "auth_array",     YA_FT_ARRAY, "auth array");

     // additional RRs, schema: [{k:v | k in [name, type, class, ttl, data_len, data]}]
     pschema_register_field(pschema, "addi_array",     YA_FT_ARRAY, "addi array");
     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "dns",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = dns_schema_reg,
    .dissectFun   = dns_dissect,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 53),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(dns)
{
    nxt_dissector_register(&gDissectorDef);
}
