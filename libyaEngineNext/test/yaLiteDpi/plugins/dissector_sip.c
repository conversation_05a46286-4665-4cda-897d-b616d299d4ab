#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_parser.h"
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/macro.h>

#define PROTO_NAME "sip"

int nxt_parser_sip_init(nxt_parser_t *parser);
int nxt_parser_sip_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata);

static
int sip_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    nxt_parser_t* parser = nxt_parser_create(nxt_parser_sip_init);
    int consume = nxt_parser_sip_parse(parser, nxt_mbuf_get_raw(mbuf, 0), nxt_mbuf_get_length(mbuf), precord);

    // 成功解析出一条消息，调整 buffer, 发布消息，将 parser 进行 reset;
    if (nxt_parser_get_status(parser) == NXT_PSTATUS_COMPLETE)
    {

    }

    // TODO: 如果解释失败了，在 record 中如何体现?

    nxt_parser_destroy(parser);
    return consume;
}

static
int sip_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db _U_)
{
     pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "sip");

     pschema_register_field(pschema, "method",      YA_FT_STRING, "");
     pschema_register_field(pschema, "version",     YA_FT_STRING, "");
     pschema_register_field(pschema, "uri",         YA_FT_STRING, "");
     pschema_register_field(pschema, "status_code", YA_FT_STRING, "");

     pschema_register_field(pschema, "via",       YA_FT_STRING, "");
     pschema_register_field(pschema, "call_id",   YA_FT_STRING, "");

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "sip",
    .schemaRegFun = sip_schema_reg,
    .dissectFun   = sip_dissect,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 5060),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(sip)
{
    nxt_dissector_register(&gDissectorDef);
}
