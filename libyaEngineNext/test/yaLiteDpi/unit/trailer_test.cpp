#include "dissector.h"
#include <gtest/gtest.h>
#include <netinet/in.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_mbuf.h>
#include <yaEngineNext/nxt_dissector.h>
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/allocator.h>
#include <yaBasicUtils/macro.h>
#include <string.h>
#include <yaFtypes/fvalue.h>
#include <stddef.h>
#include <netinet/in.h>

#define PADDING_LEN 100
static std::vector<uint8_t> gPkthead{0x11, 0x10, 0xca, 0x3c, 0x24, 0x75, 0x00, 0x04, 0x02, 0x7a, 0x00, 0x51, 0x08, 0x00, 0x45,
    0x00, 0x02, 0x1b, 0x00, 0x00, 0x40, 0x00, 0x40, 0x06, 0xab, 0xc1, 0x0a, 0x49, 0xaf, 0x03, 0x72, 0x6e, 0x61, 0x61, 0xef, 0x0f,
    0x00, 0x50, 0xc7, 0xf6, 0xbd, 0x35, 0xf2, 0x66, 0x13, 0xdd, 0x50, 0x18, 0x20, 0x00, 0xfe, 0xe3, 0x00, 0x00, 0x47, 0x45, 0x54,
    0x20, 0x2f, 0x64, 0x3f, 0x69, 0x64, 0x3d, 0x32, 0x35, 0x31, 0x39, 0x36, 0x26, 0x74, 0x74, 0x6c, 0x3d, 0x31, 0x26, 0x64, 0x6e,
    0x3d, 0x42, 0x36, 0x45, 0x34, 0x37, 0x41, 0x44, 0x41, 0x30, 0x44, 0x32, 0x42, 0x46, 0x39, 0x39, 0x36, 0x30, 0x30, 0x30, 0x39,
    0x36, 0x46, 0x42, 0x38, 0x41, 0x33, 0x31, 0x35, 0x36, 0x41, 0x33, 0x42, 0x38, 0x34, 0x33, 0x33, 0x38, 0x39, 0x44, 0x39, 0x34,
    0x39, 0x32, 0x45, 0x30, 0x31, 0x36, 0x43, 0x34, 0x32, 0x37, 0x31, 0x46, 0x35, 0x33, 0x46, 0x43, 0x46, 0x38, 0x30, 0x43, 0x43,
    0x45, 0x46, 0x41, 0x33, 0x39, 0x46, 0x44, 0x38, 0x34, 0x36, 0x34, 0x33, 0x41, 0x35, 0x42, 0x37, 0x44, 0x42, 0x26, 0x74, 0x79,
    0x70, 0x65, 0x3d, 0x61, 0x64, 0x64, 0x72, 0x73, 0x20, 0x48, 0x54, 0x54, 0x50, 0x2f, 0x31, 0x2e, 0x31, 0x0d, 0x0a, 0x48, 0x6f,
    0x73, 0x74, 0x3a, 0x20, 0x31, 0x31, 0x34, 0x2e, 0x31, 0x31, 0x30, 0x2e, 0x39, 0x37, 0x2e, 0x39, 0x37, 0x0d, 0x0a, 0x41, 0x63,
    0x63, 0x65, 0x70, 0x74, 0x3a, 0x20, 0x2a, 0x2f, 0x2a, 0x0d, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x2d, 0x4c, 0x61, 0x6e,
    0x67, 0x75, 0x61, 0x67, 0x65, 0x3a, 0x20, 0x7a, 0x68, 0x2d, 0x43, 0x4e, 0x2c, 0x7a, 0x68, 0x2d, 0x48, 0x61, 0x6e, 0x73, 0x3b,
    0x71, 0x3d, 0x30, 0x2e, 0x39, 0x0d, 0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x20, 0x6b, 0x65,
    0x65, 0x70, 0x2d, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x0d, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x2d, 0x45, 0x6e, 0x63, 0x6f,
    0x64, 0x69, 0x6e, 0x67, 0x3a, 0x20, 0x67, 0x7a, 0x69, 0x70, 0x2c, 0x20, 0x64, 0x65, 0x66, 0x6c, 0x61, 0x74, 0x65, 0x0d, 0x0a,
    0x55, 0x73, 0x65, 0x72, 0x2d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x3a, 0x20, 0x4d, 0x6f, 0x7a, 0x69, 0x6c, 0x6c, 0x61, 0x2f, 0x35,
    0x2e, 0x30, 0x20, 0x28, 0x69, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x3b, 0x20, 0x43, 0x50, 0x55, 0x20, 0x69, 0x50, 0x68, 0x6f, 0x6e,
    0x65, 0x20, 0x4f, 0x53, 0x20, 0x31, 0x36, 0x5f, 0x37, 0x5f, 0x31, 0x31, 0x20, 0x6c, 0x69, 0x6b, 0x65, 0x20, 0x4d, 0x61, 0x63,
    0x20, 0x4f, 0x53, 0x20, 0x58, 0x29, 0x20, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x57, 0x65, 0x62, 0x4b, 0x69, 0x74, 0x2f, 0x36, 0x30,
    0x35, 0x2e, 0x31, 0x2e, 0x31, 0x35, 0x20, 0x28, 0x4b, 0x48, 0x54, 0x4d, 0x4c, 0x2c, 0x20, 0x6c, 0x69, 0x6b, 0x65, 0x20, 0x47,
    0x65, 0x63, 0x6b, 0x6f, 0x29, 0x20, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2f, 0x31, 0x35, 0x45, 0x31, 0x34, 0x38, 0x20, 0x3d,
    0x3d, 0x3d, 0x20, 0x20, 0x69, 0x4f, 0x53, 0x2f, 0x31, 0x36, 0x2e, 0x37, 0x2e, 0x31, 0x31, 0x20, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
    0x2f, 0x69, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x31, 0x30, 0x2c, 0x33, 0x20, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x2f,
    0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x75, 0x6e, 0x6d, 0x65, 0x6e, 0x67, 0x2e, 0x70, 0x69, 0x6e, 0x64, 0x75, 0x6f, 0x64, 0x75, 0x6f,
    0x20, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x37, 0x2e, 0x35, 0x37, 0x2e, 0x30, 0x20, 0x41, 0x70,
    0x70, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x2f, 0x32, 0x30, 0x32, 0x35, 0x30, 0x34, 0x32, 0x37, 0x31, 0x31, 0x30, 0x35, 0x20, 0x70,
    0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x30, 0x0d, 0x0a, 0x0d, 0x0a};

static std::vector<uint8_t> gPadding(PADDING_LEN, 0x00);
static std::vector<uint8_t> gTrailer{0xff, 0xfe, 0x01, 0x08, 0x64, 0x10, 0x01, 0x01, 0x36, 0x34, 0x39, 0xf4, 0x02, 0x08, 0x68,
    0x81, 0x40, 0x73, 0x01, 0x05, 0xf1, 0xff, 0x03, 0x08, 0x53, 0x76, 0x22, 0x80, 0x45, 0x79, 0x28, 0x65, 0x07, 0x07, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x0a, 0x05, 0x64, 0xf0, 0x11, 0xe0, 0x11, 0x0b, 0x07, 0x64, 0xf0, 0x11, 0x00, 0x52, 0x87, 0x04, 0x11, 0x05, 0x00, 0x00,
    0x00, 0x00, 0x00};

static int ether_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf) {
  uint16_t trailerLen = nxt_mbuf_get_uint16_ntoh(mbuf, 10);
  nxt_engine_layer_set_trailer_len(engine, trailerLen);
  uint16_t type = nxt_mbuf_get_uint16_ntoh(mbuf, 12);
  nxt_handoff_set_key_of_number(engine, type);
  return 14;
}

#define ETHNAME "eth_trailer"
#define TRAILER_NAME "test_trailer"

static int ether_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db _U_) {
  pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, ETHNAME, "ethernet");
  return 0;
}
static int trailer_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db _U_) {
  pschema_register_base(db, "4G", "4G base layer", "#NONE");
  return 0;
}

static int trailer_dissect(nxt_engine_t *engine _U_, nxt_session_t *session _U_, nxt_mbuf_t *mbuf) {
  uint8_t *buff = (uint8_t *)nxt_mbuf_get_raw(mbuf, 0);
  int      ret = memcmp(buff, gTrailer.data(), gTrailer.size());
  EXPECT_EQ(ret, 0);

  return nxt_mbuf_get_length(mbuf);
}

static nxt_dissector_def_t *gSEthDissectorDef = NULL;
static nxt_dissector_def_t *gSTrailerDissectorDef = NULL;

TEST(dissector, set_trailer_len) {
  //已知mountAt数组只存在一个
  size_t totSize = sizeof(nxt_dissector_def_t) + sizeof(nxt_handoff_mnt_t);
  gSEthDissectorDef = static_cast<nxt_dissector_def_t *>(malloc(totSize));

  memset(gSEthDissectorDef, 0, totSize);
  gSEthDissectorDef->name = ETHNAME;
  gSEthDissectorDef->type = NXT_DISSECTOR_TYPE_LINK;
  gSEthDissectorDef->schemaRegFun = ether_schema_reg;
  gSEthDissectorDef->dissectFun = ether_dissect;
  gSEthDissectorDef->handoff = NXT_HANDOFF_DEFAULT;
  gSEthDissectorDef->mountAt->proto = 0;
  gSEthDissectorDef->mountAt->key.type = NXT_HANDOFF_TYPE_NONE;
  gSEthDissectorDef->mountAt->key.number = 0;
  nxt_dissector_register(gSEthDissectorDef);
  nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, ETHNAME, 0x0800, "ipv4");

  gSTrailerDissectorDef = static_cast<nxt_dissector_def_t *>(malloc(totSize));
  memset(gSTrailerDissectorDef, 0, totSize);
  gSTrailerDissectorDef->name = TRAILER_NAME;
  gSTrailerDissectorDef->type = NXT_DISSECTOR_TYPE_TRAILER;
  gSTrailerDissectorDef->schemaRegFun = trailer_schema_reg;
  gSTrailerDissectorDef->dissectFun = trailer_dissect;
  gSTrailerDissectorDef->mountAt->proto = 0;
  gSTrailerDissectorDef->mountAt->key.type = NXT_HANDOFF_TYPE_NONE;
  gSTrailerDissectorDef->mountAt->key.number = 0;
  nxt_dissector_register(gSTrailerDissectorDef);

  //重新将刚注册的测试协议加载到全局解析器中
  nxt_dissector_load_dissectors_from_registry();

  nxt_engine_config_t config = {.linkName = ETHNAME, .trailerName = TRAILER_NAME};
  nxt_engine_t       *engine = nxt_engine_create(&config);

  std::vector<uint8_t> pkt;
  pkt.insert(pkt.end(), gPkthead.begin(), gPkthead.end());
  pkt.insert(pkt.end(), gPadding.begin(), gPadding.end());
  pkt.insert(pkt.end(), gTrailer.begin(), gTrailer.end());

  nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), pkt.data(), pkt.size());
  nxt_engine_run(engine, mbuf);
  nxt_mbuf_free_wa(nxt_engine_get_allocator(engine),mbuf);
  nxt_engine_destroy(engine);
}

NXT_DISSECTOR_FINI(trailer) {
  free(gSEthDissectorDef);
  free(gSTrailerDissectorDef);
}
