#include <gtest/gtest.h>
#include <yaEngineNext/nxt_engine.h>
#include "dissector.h"

TEST(dissector, add_new_rule)
{
    // type number
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "eth", 0x40, "tcp");
    // type proto payload
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_PORT_PAYLOAD, "tcp", 8080, "hello", "rtsp");

    nxt_engine_t *engine = nxt_engine_create(NULL);

    nxt_Dissector *dissector = nxt_dissector_get_by_name("eth");
    EXPECT_NE(nullptr, dissector);

    // check next number dissector
    nxt_handoff_key_t keyNumber = {.type = NXT_HANDOFF_TYPE_NUMBER, {.number = 0x40}};
    nxt_Dissector *nDissector = dissector->findNextDissector(keyNumber, NULL, NULL);
    EXPECT_STREQ("tcp", nDissector->getProtoName());

    // check next port payload dissector
    nxt_handoff_key_t keyPayload = {
        .type = NXT_HANDOFF_TYPE_PORT_PAYLOAD,
        {
            .portPayload = {
                .serverPort = 8080,
                .payloadLen = 5,
                .payloadData = (uint8_t *)"hello",
            }
        }
    };
    dissector = nxt_dissector_get_by_name("tcp");
    nxt_Dissector *pnDissector = dissector->findNextDissector(keyPayload, NULL, NULL);
    EXPECT_STREQ("rtsp", pnDissector->getProtoName());

    nxt_engine_destroy(engine);
}
