#include "util/timer.h"
#include "yaEngineNext/nxt_util.h"
#include <yaBasicUtils/macro.h>
#include <gmock/gmock.h>
#include <unistd.h>

int gRunFlag = 1;

void nxt_timer_callback(nxt_timer_t *timer _U_, void *userdata _U_)
{
    gRunFlag = 0;

    printf("timer event fired\n");
}

TEST(timer, one_shot)
{
    ya_allocator_t         *alloc = ya_allocator_get_default();
    nxt_timer_scheduler_t*  s     = nxt_timer_scheduler_create(alloc, 10);
    nxt_timer_t*            t     = nxt_timer_create(alloc, s, nxt_timer_callback, s);

    nxt_timer_start(s, t, 100, 1);

    while (gRunFlag)
    {
        nxt_timer_scheduler_update(s, nxt_utils_current_time_ms());
        usleep(5000); // 5ms
    }

    nxt_timer_destroy(alloc, s, t);
    nxt_timer_scheduler_destroy(alloc, s);
}
