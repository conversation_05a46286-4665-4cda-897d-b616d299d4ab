#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_parser.h>
#include <yaProtoRecord/precord.h>
#include <gmock/gmock.h>

#include <string>

#define CHAR_ARRAY_LEN(array) (sizeof(array) - 1)

using namespace testing;

extern "C"
{
    int nxt_parser_http_init(nxt_parser_t *parser);
    int nxt_parser_http_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata);
}

static
const char *precord_get_str_fvalue(precord_t *precord, const char *fname)
{
    ya_fvalue_t *fvalue = precord_fvalue_get(precord, fname);
    if (NULL == fvalue)
    {
        return NULL;
    }

    return ya_fvalue_get_string(fvalue);
}

class http_parser : public testing::Test
{
public:
    void SetUp() override
    {
        engine_ = nxt_engine_create(NULL);
        pschema_db_t* db = nxt_engine_get_schemadb(engine_);
        precord_ = precord_create_ex(db, PRECORD_FLAG_NONE);
        precord_layer_put_new_layer_cache(precord_, "http");
    }

    void TearDown() override
    {
        precord_destroy(precord_);
        nxt_engine_destroy(engine_);
    }

protected:
    nxt_engine_t *engine_  = NULL;
    precord_t    *precord_ = NULL;
};

TEST_F(http_parser, basic)
{
    const uint8_t kInputMsg[] = "GET /seupdater.gif?h=02978BFB18E163AE2B4CB705ECB1A44F&elapse=19949969&res=0 HTTP/1.1\r\n"
                                "Host: p3p.sogou.com\r\n"
                                "Cache-Control: no-cache\r\n"
                                "User-Agent: MicroMessenger Client\r\n"
                                "Cookie: IMEVER=9.8.0.3746\r\n\r\n";

    nxt_parser_t *parser      = nxt_parser_create(nxt_parser_http_init);
    int           consumeLen  = nxt_parser_http_parse(parser, kInputMsg, CHAR_ARRAY_LEN(kInputMsg), (void *)precord_);
    ASSERT_EQ(NXT_PSTATUS_COMPLETE,      nxt_parser_get_status(parser));
    ASSERT_EQ(CHAR_ARRAY_LEN(kInputMsg), consumeLen);

    EXPECT_STREQ("/seupdater.gif?h=02978BFB18E163AE2B4CB705ECB1A44F&elapse=19949969&res=0",
                 precord_get_str_fvalue(precord_, "URI"));

    EXPECT_STREQ("GET",                  precord_get_str_fvalue(precord_, "Method"));
    EXPECT_STREQ("p3p.sogou.com",        precord_get_str_fvalue(precord_, "Host"));
    EXPECT_STREQ("IMEVER=9.8.0.3746",    precord_get_str_fvalue(precord_, "Cookie"));
    EXPECT_STREQ("MicroMessenger Client",precord_get_str_fvalue(precord_, "User-Agent"));

    nxt_parser_destroy(parser);
}

TEST_F(http_parser, lengthed_body)
{
    std::string msgHeader = "GET /seupdater.gif?h=02978BFB18E163AE2B4CB705ECB1A44F&elapse=19949969&res=0 HTTP/1.1\r\n"
                             "Host: p3p.sogou.com\r\n"
                             "Cache-Control: no-cache\r\n"
                             "User-Agent: MicroMessenger Client\r\n"
                             "Cookie: IMEVER=9.8.0.3746\r\n"
                             "Content-Length: 12\r\n"
                             "\r\n";
    std::string msgBody   = "hello, world";
    std::string msg       = msgHeader + msgBody;

    nxt_parser_t *parser      = nxt_parser_create(nxt_parser_http_init);
    int           consumeLen  = nxt_parser_http_parse(parser, (const uint8_t *)msg.c_str(), msg.length(), (void *)precord_);
    ASSERT_EQ(NXT_PSTATUS_COMPLETE,      nxt_parser_get_status(parser));
    ASSERT_EQ(msgHeader.length(), consumeLen);
    EXPECT_STREQ("GET",                 precord_get_str_fvalue(precord_, "Method"));
    EXPECT_STREQ("p3p.sogou.com",       precord_get_str_fvalue(precord_, "Host"));
    EXPECT_STREQ("12",                  precord_get_str_fvalue(precord_, "Content-Length"));

    nxt_parser_destroy(parser);
}

TEST_F(http_parser, chunked_body)
{
    std::string msgHeader = "GET /seupdater.gif?h=02978BFB18E163AE2B4CB705ECB1A44F&elapse=19949969&res=0 HTTP/1.1\r\n"
                             "Host: p3p.sogou.com\r\n"
                             "Cache-Control: no-cache\r\n"
                             "User-Agent: MicroMessenger Client\r\n"
                             "Cookie: IMEVER=9.8.0.3746\r\n"
                             "Transfer-Encoding: chunked\r\n"
                             "\r\n";
    std::string msgBody   = "c\r\n"
                             "hello, world";
    std::string msg = msgHeader + msgBody;

    nxt_parser_t *parser      = nxt_parser_create(nxt_parser_http_init);
    int           consumeLen  = nxt_parser_http_parse(parser, (const uint8_t *)msg.c_str(), msg.length(), (void *)precord_);
    ASSERT_EQ(NXT_PSTATUS_COMPLETE,      nxt_parser_get_status(parser));
    ASSERT_EQ(msgHeader.length(), consumeLen);
    EXPECT_STREQ("GET",                 precord_get_str_fvalue(precord_, "Method"));
    EXPECT_STREQ("p3p.sogou.com",       precord_get_str_fvalue(precord_, "Host"));
    EXPECT_STREQ("chunked",             precord_get_str_fvalue(precord_, "Transfer-Encoding"));

    nxt_parser_destroy(parser);
}

TEST_F(http_parser, pipeline)
{
#if 0
    const uint8_t input_msg[] = "GET /seupdater.gif?h=02978BFB18E163AE2B4CB705ECB1A44F&elapse=19949969&res=0 HTTP/1.1\r\n"
                                "Host: p3p.sogou.com\r\n"
                                "Cache-Control: no-cache\r\n"
                                "User-Agent: MicroMessenger Client\r\n"
                                "Cookie: IMEVER=9.8.0.3746\r\n"
                                "\r\n"
                                "hello, world"
                                "POST /seupdater.gif HTTP/1.1\r\n"
                                "Host: p3p.sogou2.com\r\n"
                                "\r\n"
                                "hello, world";
#endif
}
