#include "engine.h"
#include "session.h"
#include <gmock/gmock.h>

TEST(session_test, session_alloc_get_correct_size)
{
    nxt_Engine *engine = nxt_engine_create(NULL);

    nxt_tuple_5_ipv4_t t5{6, 1, 2, 80, 19001};
    nxt_Session *session = engine->getIpv4TcpSessionKeeper()->newTcpSession(&t5, NULL, NULL, NULL);

    const int kBuffSizeA = 50;
    const int kBuffSizeB = 20;
    char *buffA = (char *)session->alloc(kBuffSizeA);
    char *buffB = (char *)session->alloc(kBuffSizeB);

    // buffA 与 buffB 分别由 session 分配出来，
    // 它们可能相邻, 但是分别对 buffB 与 BuffA 进行写操作，它们必须互不影响;
    // 如果 buffB 的内容被 buffA 的写操作改变了，
    // 说明 session->alloc 没有按照正确的内存尺寸来分配内存;
    strncpy(buffB, "hello", kBuffSizeB);
    memset(buffA, 'x', kBuffSizeA);
    ASSERT_STREQ(buffB, "hello") << "session->alloc(n) can not alloc correct size memory, they are overlapped.";

    nxt_engine_destroy(engine);
}
