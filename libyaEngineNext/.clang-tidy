---
Checks:          'clang-diagnostic-*,clang-analyzer-*,readability-identifier-naming'
WarningsAsErrors: ''
HeaderFilterRegex: ''
AnalyzeTemporaryDtors: false
FormatStyle:     none
User:            zhengsw
CheckOptions:
  llvm-else-after-return.WarnOnConditionVariables: 'false'
  modernize-loop-convert.MinConfidence: reasonable
  modernize-replace-auto-ptr.IncludeStyle: llvm
  modernize-pass-by-value.IncludeStyle: llvm
  google-readability-namespace-comments.ShortNamespaceLines: '10'
  google-readability-namespace-comments.SpacesBeforeComments: '2'
  cppcoreguidelines-non-private-member-variables-in-classes.IgnoreClassesWithAllMemberVariablesBeingPublic: 'true'
  google-readability-braces-around-statements.ShortStatementLines: '1'
  cert-err33-c.CheckedFunctions: '::aligned_alloc;::asctime_s;::at_quick_exit;::atexit;::bsearch;::bsearch_s;::btowc;::c16rtomb;::c32rtomb;::calloc;::clock;::cnd_broadcast;::cnd_init;::cnd_signal;::cnd_timedwait;::cnd_wait;::ctime_s;::fclose;::fflush;::fgetc;::fgetpos;::fgets;::fgetwc;::fopen;::fopen_s;::fprintf;::fprintf_s;::fputc;::fputs;::fputwc;::fputws;::fread;::freopen;::freopen_s;::fscanf;::fscanf_s;::fseek;::fsetpos;::ftell;::fwprintf;::fwprintf_s;::fwrite;::fwscanf;::fwscanf_s;::getc;::getchar;::getenv;::getenv_s;::gets_s;::getwc;::getwchar;::gmtime;::gmtime_s;::localtime;::localtime_s;::malloc;::mbrtoc16;::mbrtoc32;::mbsrtowcs;::mbsrtowcs_s;::mbstowcs;::mbstowcs_s;::memchr;::mktime;::mtx_init;::mtx_lock;::mtx_timedlock;::mtx_trylock;::mtx_unlock;::printf_s;::putc;::putwc;::raise;::realloc;::remove;::rename;::scanf;::scanf_s;::setlocale;::setvbuf;::signal;::snprintf;::snprintf_s;::sprintf;::sprintf_s;::sscanf;::sscanf_s;::strchr;::strerror_s;::strftime;::strpbrk;::strrchr;::strstr;::strtod;::strtof;::strtoimax;::strtok;::strtok_s;::strtol;::strtold;::strtoll;::strtoul;::strtoull;::strtoumax;::strxfrm;::swprintf;::swprintf_s;::swscanf;::swscanf_s;::thrd_create;::thrd_detach;::thrd_join;::thrd_sleep;::time;::timespec_get;::tmpfile;::tmpfile_s;::tmpnam;::tmpnam_s;::tss_create;::tss_get;::tss_set;::ungetc;::ungetwc;::vfprintf;::vfprintf_s;::vfscanf;::vfscanf_s;::vfwprintf;::vfwprintf_s;::vfwscanf;::vfwscanf_s;::vprintf_s;::vscanf;::vscanf_s;::vsnprintf;::vsnprintf_s;::vsprintf;::vsprintf_s;::vsscanf;::vsscanf_s;::vswprintf;::vswprintf_s;::vswscanf;::vswscanf_s;::vwprintf_s;::vwscanf;::vwscanf_s;::wcrtomb;::wcschr;::wcsftime;::wcspbrk;::wcsrchr;::wcsrtombs;::wcsrtombs_s;::wcsstr;::wcstod;::wcstof;::wcstoimax;::wcstok;::wcstok_s;::wcstol;::wcstold;::wcstoll;::wcstombs;::wcstombs_s;::wcstoul;::wcstoull;::wcstoumax;::wcsxfrm;::wctob;::wctrans;::wctype;::wmemchr;::wprintf_s;::wscanf;::wscanf_s;'
  modernize-loop-convert.MaxCopySize: '16'
  cert-dcl16-c.NewSuffixes: 'L;LL;LU;LLU'
  cert-oop54-cpp.WarnOnlyIfThisHasSuspiciousField: 'false'
  cert-str34-c.DiagnoseSignedUnsignedCharComparisons: 'false'
  modernize-use-nullptr.NullMacros: 'NULL'
  llvm-qualified-auto.AddConstToQualified: 'false'
  modernize-loop-convert.NamingStyle: CamelCase
  llvm-else-after-return.WarnOnUnfixable: 'false'
  google-readability-function-size.StatementThreshold: '800'
  # readability-identifier-length.MinimumParameterNameLength: 1
  # readability-identifier-length.MinimumVariableNameLength: 1

  # 结构体, 可选前缀为: "nxt_"
  # readability-identifier-naming.StructCase: aNy_CasE
  # readability-identifier-naming.StructPrefix: nxt_

  # enum
  readability-identifier-naming.EnumCase: lower_case
  readability-identifier-naming.EnumConstantCase: UPPER_CASE

  # 宏, 允许 __ITIMER_H__, 但 clang-tidy 无法支持;
  # readability-identifier-naming.MacroDefinitionCase: UPPER_CASE

  # typedef
  readability-identifier-naming.TypedefCase: lower_case
  # readability-identifier-naming.TypedefSuffix: '(_t|_enum)'

  # 类, 允许 nxt_Engine 或者 Engine, 但 clang-tidy 无法支持;
  # readability-identifier-naming.ClassCase: CamelCase
  # readability-identifier-naming.ClassPrefix: nxt_
  readability-identifier-naming.PrivateMemberCase: camelBack
  readability-identifier-naming.PrivateMemberSuffix: _
  readability-identifier-naming.ProtectedMemberCase: camelBack
  readability-identifier-naming.ProtectedMemberSuffix: _
  readability-identifier-naming.PrivateMethodCase: camelBack
  readability-identifier-naming.PublicMemberCase: camelBack
  readability-identifier-naming.PublicMethodCase: camelBack
  readability-identifier-naming.ClassMemberCase: CamelCase
  readability-identifier-naming.ClassMemberPrefix: c

  # 变量
  readability-identifier-naming.GlobalVariableCase: CamelCase
  readability-identifier-naming.GlobalVariablePrefix: g
  readability-identifier-naming.LocalVariableCase: camelBack
  readability-identifier-naming.ParameterCase: camelBack
  readability-identifier-naming.StaticVariableCase: CamelCase
  readability-identifier-naming.StaticVariablePrefix: s

  # 常量
  readability-identifier-naming.GlobalConstantCase: CamelCase
  readability-identifier-naming.GlobalConstantPrefix: k
  readability-identifier-naming.LocalConstantCase: CamelCase
  readability-identifier-naming.LocalConstantPrefix: k

  # 全局函数
  readability-identifier-naming.GlobalFunctionCase: lower_case
...
